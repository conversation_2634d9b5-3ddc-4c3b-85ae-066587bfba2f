//package com.miner.common.translation.impl;
//
//import com.miner.common.annotation.TranslationType;
//import com.miner.common.constant.TransConstant;
//import com.miner.common.core.service.OssService;
//import com.miner.common.translation.TranslationInterface;
//import lombok.AllArgsConstructor;
//import org.springframework.stereotype.Component;
//
///**
// * OSS翻译实现
// *
// * <AUTHOR> Li
// */
//@Component
//@AllArgsConstructor
//@TranslationType(type = TransConstant.OSS_ID_TO_URL)
//public class OssUrlTranslationImpl implements TranslationInterface<String> {
//
//    private final OssService ossService;
//
//    @Override
//    public String translation(Object key, String other) {
//        return ossService.selectUrlByIds(key.toString());
//    }
//}
