package com.miner.strategy.signal.impl;

import com.miner.strategy.calculator.TechnicalIndicatorCalculator;
import com.miner.strategy.signal.SignalStrengthCalculator;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RSI信号强度计算器（做空）
 */
@Component
@Slf4j
public class RSIShortSignalCalculator implements SignalStrengthCalculator {

    @Autowired
    private TechnicalIndicatorCalculator indicatorCalculator;

    private static final int RSI_PERIOD = 14;
    private static final double RSI_OVERBOUGHT = 70.0;
    private static final double RSI_FALLING = 60.0;
    private static final double WEIGHT = 2.0;

    @Override
    public double calculateStrength(List<KLineEntity> klineList, SignalDirection direction) {
        if (direction != SignalDirection.SHORT) {
            return 0.0;
        }

        if (klineList.size() < RSI_PERIOD + 3) {
            return 0.0;
        }

        String instId = klineList.get(0).getInstId();

        // 计算当前和历史RSI值
        List<Double> closePrices = indicatorCalculator.extractClosePrices(klineList);
        double currentRsi = calculateRSI(closePrices, 0);
        double previousRsi = calculateRSI(closePrices, 1);
        double prevPreviousRsi = calculateRSI(closePrices, 2);

        // 检查是否有过超买
        double highestRsi = Math.max(Math.max(currentRsi, previousRsi), prevPreviousRsi);
        boolean wasOverbought = highestRsi > RSI_OVERBOUGHT;

        // 检查RSI下降趋势
        boolean isFalling = currentRsi < previousRsi && previousRsi < prevPreviousRsi;

        if (wasOverbought && currentRsi < previousRsi) {
            // 计算从超买区回落的幅度
            double fallFromOverbought = 0.0;
            if (highestRsi > RSI_OVERBOUGHT) {
                double denominator = highestRsi - 50.0;
                if (denominator > 5.0) {
                    fallFromOverbought = (highestRsi - currentRsi) / denominator;
                    fallFromOverbought = Math.min(fallFromOverbought, 1.0);
                } else {
                    fallFromOverbought = Math.min((highestRsi - currentRsi) / 20.0, 1.0);
                }
            }

            // 下降速度（前一个RSI与当前的差值）
            double fallSpeed = (previousRsi - currentRsi) / 10.0;
            fallSpeed = Math.min(fallSpeed, 1.0);

            // 趋势确认（连续下降）
            double trendScore = isFalling ? 0.3 : 0.0;

            // 综合评分
            return Math.min(fallFromOverbought * 0.5 + fallSpeed * 0.2 + trendScore, 1.0);
        }

        return 0.0;
    }

    @Override
    public String getName() {
        return "RSI_SHORT";
    }

    @Override
    public double getWeight() {
        return WEIGHT;
    }

    private double calculateRSI(List<Double> prices, int offset) {
        if (prices.size() < offset + RSI_PERIOD + 1) {
            return 50.0;
        }

        double gainSum = 0.0;
        double lossSum = 0.0;

        for (int i = offset + 1; i <= offset + RSI_PERIOD; i++) {
            double change = prices.get(i) - prices.get(i - 1);
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += Math.abs(change);
            }
        }

        if (lossSum == 0) return 100.0;
        if (gainSum == 0) return 0.0;

        double avgGain = gainSum / RSI_PERIOD;
        double avgLoss = lossSum / RSI_PERIOD;
        double rs = avgGain / avgLoss;

        return 100.0 - (100.0 / (1.0 + rs));
    }
}
