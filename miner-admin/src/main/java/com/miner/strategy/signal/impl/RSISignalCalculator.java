package com.miner.strategy.signal.impl;

import com.miner.strategy.calculator.TechnicalIndicatorCalculator;
import com.miner.strategy.signal.SignalStrengthCalculator;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RSI信号强度计算器（做多）
 */
@Component
@Slf4j
public class RSISignalCalculator implements SignalStrengthCalculator {

    @Autowired
    private TechnicalIndicatorCalculator indicatorCalculator;

    private static final int RSI_PERIOD = 14;
    private static final double RSI_OVERSOLD = 30.0;
    private static final double RSI_RISING = 40.0;
    private static final double WEIGHT = 2.0;

    @Override
    public double calculateStrength(List<KLineEntity> klineList, SignalDirection direction) {
        if (direction != SignalDirection.LONG) {
            return 0.0;
        }

        if (klineList.size() < RSI_PERIOD + 3) {
            return 0.0;
        }

        String instId = klineList.get(0).getInstId();

        // 计算当前和历史RSI值
        List<Double> closePrices = indicatorCalculator.extractClosePrices(klineList);
        double currentRsi = calculateRSI(closePrices, 0);
        double previousRsi = calculateRSI(closePrices, 1);
        double prevPreviousRsi = calculateRSI(closePrices, 2);

        // 检查是否有过超卖
        double lowestRsi = Math.min(Math.min(currentRsi, previousRsi), prevPreviousRsi);
        boolean wasOversold = lowestRsi < RSI_OVERSOLD;

        // 检查RSI上升趋势
        boolean isRising = currentRsi > previousRsi && previousRsi > prevPreviousRsi;

        if (wasOversold && currentRsi > previousRsi) {
            // 计算从超卖区回升的幅度
            double riseFromOversold = 0.0;
            if (lowestRsi < RSI_OVERSOLD) {
                double denominator = 50.0 - lowestRsi;
                if (denominator > 5.0) {
                    riseFromOversold = (currentRsi - lowestRsi) / denominator;
                    riseFromOversold = Math.min(riseFromOversold, 1.0);
                } else {
                    riseFromOversold = Math.min((currentRsi - lowestRsi) / 20.0, 1.0);
                }
            }

            // 上升速度（当前RSI与前一个的差值）
            double riseSpeed = (currentRsi - previousRsi) / 10.0;
            riseSpeed = Math.min(riseSpeed, 1.0);

            // 趋势确认（连续上升）
            double trendScore = isRising ? 0.3 : 0.0;

            // 综合评分
            return Math.min(riseFromOversold * 0.5 + riseSpeed * 0.2 + trendScore, 1.0);
        }

        return 0.0;
    }

    @Override
    public String getName() {
        return "RSI";
    }

    @Override
    public double getWeight() {
        return WEIGHT;
    }

    private double calculateRSI(List<Double> prices, int offset) {
        if (prices.size() < offset + RSI_PERIOD + 1) {
            return 50.0;
        }

        double gainSum = 0.0;
        double lossSum = 0.0;

        for (int i = offset + 1; i <= offset + RSI_PERIOD; i++) {
            double change = prices.get(i) - prices.get(i - 1);
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += Math.abs(change);
            }
        }

        if (lossSum == 0) return 100.0;
        if (gainSum == 0) return 0.0;

        double avgGain = gainSum / RSI_PERIOD;
        double avgLoss = lossSum / RSI_PERIOD;
        double rs = avgGain / avgLoss;

        return 100.0 - (100.0 / (1.0 + rs));
    }
}
