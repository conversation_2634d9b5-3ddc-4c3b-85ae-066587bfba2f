package com.miner.strategy.signal;


import com.miner.system.indicator.KLineEntity;

import java.util.List;

/**
 * 信号强度计算器接口
 * 使用策略模式，不同的技术指标实现不同的信号强度计算
 */
public interface SignalStrengthCalculator {

    /**
     * 计算信号强度
     * @param klineList K线数据
     * @param direction 方向：LONG(做多) 或 SHORT(做空)
     * @return 信号强度 (0.0 - 1.0)
     */
    double calculateStrength(List<KLineEntity> klineList, SignalDirection direction);

    /**
     * 获取计算器名称
     */
    String getName();

    /**
     * 获取权重
     */
    double getWeight();

    /**
     * 信号方向枚举
     */
    enum SignalDirection {
        LONG,   // 做多
        SHORT   // 做空
    }
}
