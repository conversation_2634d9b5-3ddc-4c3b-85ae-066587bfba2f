package com.miner.strategy.signal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信号强度计算器工厂
 * 使用工厂模式管理各种信号强度计算器
 */
@Component
public class SignalCalculatorFactory {

    @Autowired
    private List<SignalStrengthCalculator> calculators;

    private Map<String, SignalStrengthCalculator> calculatorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (SignalStrengthCalculator calculator : calculators) {
            calculatorMap.put(calculator.getName(), calculator);
        }
    }

    /**
     * 获取指定名称的计算器
     */
    public SignalStrengthCalculator getCalculator(String name) {
        return calculatorMap.get(name);
    }

    /**
     * 获取所有计算器
     */
    public Map<String, SignalStrengthCalculator> getAllCalculators() {
        return calculatorMap;
    }

    /**
     * 获取做多信号计算器列表
     */
    public Map<String, SignalStrengthCalculator> getLongCalculators() {
        Map<String, SignalStrengthCalculator> longCalculators = new HashMap<>();
        longCalculators.put("RSI", calculatorMap.get("RSI"));
        longCalculators.put("MACD", calculatorMap.get("MACD"));
        longCalculators.put("ADX", calculatorMap.get("ADX"));
        longCalculators.put("BOLLINGER", calculatorMap.get("BOLLINGER"));
        longCalculators.put("CANDLESTICK", calculatorMap.get("CANDLESTICK"));
        longCalculators.put("VOLUME", calculatorMap.get("VOLUME"));
        longCalculators.put("PRICE_BREAKOUT", calculatorMap.get("PRICE_BREAKOUT"));
        return longCalculators;
    }

    /**
     * 获取做空信号计算器列表
     */
    public Map<String, SignalStrengthCalculator> getShortCalculators() {
        Map<String, SignalStrengthCalculator> shortCalculators = new HashMap<>();
        shortCalculators.put("RSI_SHORT", calculatorMap.get("RSI_SHORT"));
        shortCalculators.put("MACD_SHORT", calculatorMap.get("MACD_SHORT"));
        shortCalculators.put("ADX_SHORT", calculatorMap.get("ADX_SHORT"));
        shortCalculators.put("BOLLINGER_SHORT", calculatorMap.get("BOLLINGER_SHORT"));
        shortCalculators.put("CANDLESTICK_SHORT", calculatorMap.get("CANDLESTICK_SHORT"));
        shortCalculators.put("VOLUME_SHORT", calculatorMap.get("VOLUME_SHORT"));
        shortCalculators.put("PRICE_BREAKDOWN", calculatorMap.get("PRICE_BREAKDOWN"));
        return shortCalculators;
    }
}
