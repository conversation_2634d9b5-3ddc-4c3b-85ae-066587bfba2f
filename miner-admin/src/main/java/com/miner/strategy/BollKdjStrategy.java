package com.miner.strategy;

import com.miner.common.utils.redis.RedisUtils;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.okx.bean.Position;
import jodd.util.ThreadUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 布林带KDJ综合策略
 */
@Component
@Slf4j
public class BollKdjStrategy extends BaseStrategy {

    // 基础策略参数
    private static final double BASE_TP_RATIO = 1.2;
    private static final double BASE_SL_RATIO = 0.4;
    private static final double KDJ_ADJUST_FACTOR = 0.2;
    private static final int ADX_PERIOD = 14;
    private static final double ADX_THRESHOLD = 25.0;


    @Override
    public void run() {
        List<String> instIds = getInstIds(20);

        for (String instId : instIds) {
            String key = "BollKdjStrategy:" + instId;
            if (checkTrade(key)) {
                continue;
            }

            List<KLineEntity> kline = getKline(instId, "15m");
            BigDecimal close = kline.get(0).getClose();

            // KDJ信号判断
            boolean crossUp = kdjStrongCrossUp(kline);
            boolean kdjCrossDown = kdjStrongCrossDown(kline);

            List<KLineEntity> kline15 = kline.subList(0, 15);
            List<Double> mb = kline15.stream().mapToDouble(KLineEntity::getMb).boxed().collect(Collectors.toList());

            // 计算布林带高度和波动率
            double height = kline.get(0).getUp() - kline15.get(0).getDn();
            double volatility = calculateVolatility(kline15);

            // 计算ADX趋势强度
            double adx = calculateADX(kline, 0, ADX_PERIOD);
            double plusDI = calculatePlusDI(kline, 0, ADX_PERIOD);
            double minusDI = calculateMinusDI(kline, 0, ADX_PERIOD);

            // 多头信号判断
            if (crossUp && isUp(mb) && close.doubleValue() < kline15.get(0).getUp()) {
                if (checkHighWinRateConditions(kline, true, close)) {
                    if (adx > ADX_THRESHOLD && plusDI > minusDI) {
                        processLongSignal(instId, close, kline15, height, volatility, adx);
                        continue;
                    } else {
                        log.info("[{}]多头信号被ADX过滤，ADX={}, +DI={}, -DI={}", instId, adx, plusDI, minusDI);
                    }
                }
            }

            // 空头信号判断
            if (kdjCrossDown && isDown(mb) && close.doubleValue() > kline15.get(0).getDn()) {
                if (checkHighWinRateConditions(kline, false, close)) {
                    if (adx > ADX_THRESHOLD && minusDI > plusDI) {
                        processShortSignal(instId, close, kline15, height, volatility, adx);
                    } else {
                        log.info("[{}]空头信号被ADX过滤，ADX={}, +DI={}, -DI={}", instId, adx, plusDI, minusDI);
                    }
                }
            }
            ThreadUtil.sleep(20);
        }
    }

    /**
     * 处理多头信号
     */
    private void processLongSignal(String instId, BigDecimal close, List<KLineEntity> kline15,
                                   double height, double volatility, double adx) {
        // 使用固定的止盈止损参数，不再动态调整
        double tpRatio = BASE_TP_RATIO;
        double slRatio = BASE_SL_RATIO;

        trade(instId, "buy", getSz(instId, close.doubleValue()), "long", false);
        ThreadUtil.sleep(50);
        Position pos = getPos(instId);
        double openPrice = new BigDecimal(pos.getAvgPx()).doubleValue();

        // 计算动态止盈止损点位
        double[] tpslLevels = calculateDynamicTPSL(kline15, height, volatility, true,
            tpRatio, slRatio);
        String winPx = getPz(openPrice + tpslLevels[0], close.scale());
        String lossPx = getPz(openPrice - tpslLevels[1], close.scale());

        algoTradeWin(instId, "sell", pos.getAvailPos(), "long", winPx);
        algoTradeLoss(instId, "sell", pos.getAvailPos(), "long", lossPx);

        String key = "BollKdjStrategy:" + instId;
        RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(1));

        String title = MessageFormat.format("做多[{0}]信号", instId);
        String message = MessageFormat.format("{0}做多信号,成交价{1},止盈价{2},止损价{3},ADX={4}",
            instId, pos.getAvgPx(), winPx, lossPx, String.format("%.2f", adx));
        messageService.send(title, message);
    }

    /**
     * 处理空头信号
     */
    private void processShortSignal(String instId, BigDecimal close, List<KLineEntity> kline15,
                                    double height, double volatility, double adx) {
        // 使用固定的止盈止损参数，不再动态调整
        double tpRatio = BASE_TP_RATIO;
        double slRatio = BASE_SL_RATIO;

        trade(instId, "sell", getSz(instId, close.doubleValue()), "short", false);
        ThreadUtil.sleep(50);
        Position pos = getPos(instId);
        double openPrice = new BigDecimal(pos.getAvgPx()).doubleValue();

        // 计算动态止盈止损点位
        double[] tpslLevels = calculateDynamicTPSL(kline15, height, volatility, false,
            tpRatio, slRatio);
        String winPx = getPz(openPrice - tpslLevels[0], close.scale());
        String lossPx = getPz(openPrice + tpslLevels[1], close.scale());

        algoTradeWin(instId, "buy", pos.getAvailPos(), "short", winPx);
        algoTradeLoss(instId, "buy", pos.getAvailPos(), "short", lossPx);

        String key = "BollKdjStrategy:" + instId;
        RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(1));

        String title = MessageFormat.format("做空[{0}]信号", instId);
        String message = MessageFormat.format("{0}做空信号,成交价{1},止盈价{2},止损价{3},ADX={4}",
            instId, pos.getAvgPx(), winPx, lossPx, String.format("%.2f", adx));
        messageService.send(title, message);
    }

    /**
     * 计算动态止盈止损点位
     */
    private double[] calculateDynamicTPSL(List<KLineEntity> kline, double height, double volatility,
                                          boolean isLong, double tpRatio, double slRatio) {
        // 获取当前KDJ值
        double k = kline.get(0).getK();
        double d = kline.get(0).getD();
        double j = kline.get(0).getJ();

        // 根据KDJ值调整止盈止损比例
        double kdjStrength = Math.abs(j - d) / 100.0;
        double tpAdjust = isLong ? (k > d ? KDJ_ADJUST_FACTOR : -KDJ_ADJUST_FACTOR) : (k < d ? KDJ_ADJUST_FACTOR : -KDJ_ADJUST_FACTOR);
        double slAdjust = isLong ? (k > d ? -KDJ_ADJUST_FACTOR : KDJ_ADJUST_FACTOR) : (k < d ? -KDJ_ADJUST_FACTOR : KDJ_ADJUST_FACTOR);

        // 计算最终止盈止损比例
        double finalTPRatio = tpRatio + (tpAdjust * kdjStrength);
        double finalSLRatio = slRatio + (slAdjust * kdjStrength);

        // 根据波动率调整
        finalTPRatio *= (1 + volatility);
        finalSLRatio *= (1 + volatility);

        return new double[]{height * finalTPRatio, height * finalSLRatio};
    }

    /**
     * 计算波动率
     */
    private double calculateVolatility(List<KLineEntity> kline) {
        if (kline.size() < 2) {
            return 0;
        }

        double sum = 0;
        double count = 0;

        for (int i = 1; i < kline.size(); i++) {
            double current = kline.get(i - 1).getClose().doubleValue();
            double previous = kline.get(i).getClose().doubleValue();
            if (previous == 0) {
                continue;
            }

            double ret = (current - previous) / previous;
            sum += ret * ret;
            count++;
        }

        return count > 0 ? Math.sqrt(sum / count) : 0;
    }

    private boolean checkTrade(String key) {
        return RedisUtils.getCacheObject(key) != null;
    }

    /**
     * 判断中轨是否向上趋势
     */
    private boolean isUp(List<Double> mb) {
        int count = 0;
        for (int i = mb.size() - 1; i > 0; i--) {
            if (mb.get(i) < mb.get(i - 1)) {
                count++;
            }
        }
        return count >= mb.size() * 0.8;
    }

    /**
     * 判断中轨是否向下趋势
     */
    private boolean isDown(List<Double> mb) {
        int count = 0;
        for (int i = 1; i < mb.size(); i++) {
            if (mb.get(i) > mb.get(i - 1)) {
                count++;
            }
        }
        return count >= mb.size() * 0.8;
    }

    /**
     * 改进的KDJ金叉判断
     */
    private boolean kdjStrongCrossUp(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return false;
        }

        KLineEntity current = klineList.get(0);
        KLineEntity previous = klineList.get(1);

        boolean basicCross = current.getJ() > current.getD() && previous.getJ() <= previous.getD();
        boolean strongSignal = (current.getJ() - current.getD() > 0.5) || (current.getJ() > 30 && previous.getJ() < 30);

        return basicCross && strongSignal;
    }

    /**
     * 改进的KDJ死叉判断
     */
    private boolean kdjStrongCrossDown(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return false;
        }

        KLineEntity current = klineList.get(0);
        KLineEntity previous = klineList.get(1);

        boolean basicCross = current.getJ() < current.getD() && previous.getJ() >= previous.getD();
        boolean strongSignal = (current.getD() - current.getJ() > 0.5) || (current.getJ() < 70 && previous.getJ() > 70);

        return basicCross && strongSignal;
    }

    /**
     * 计算平均成交量
     */
    private double calculateAverageVolume(List<KLineEntity> klineList, int period) {
        if (klineList.size() < period) {
            return 0;
        }

        double sum = 0;
        for (int i = 0; i < period; i++) {
            sum += klineList.get(i).getVolume();
        }
        return sum / period;
    }

    /**
     * 计算ADX指标
     */
    private double calculateADX(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }

        // 初始化数组
        double[] tr = new double[period];
        double[] plusDM = new double[period];
        double[] minusDM = new double[period];

        // 计算TR和DM
        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;

            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);

            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            tr[i] = Math.max(Math.max(tr1, tr2), tr3);

            double upMove = high - prev.getHigh().doubleValue();
            double downMove = prev.getLow().doubleValue() - low;

            plusDM[i] = (upMove > downMove && upMove > 0) ? upMove : 0;
            minusDM[i] = (downMove > upMove && downMove > 0) ? downMove : 0;
        }

        // 计算平滑值
        double smoothedTR = Arrays.stream(tr).sum();
        double smoothedPlusDM = Arrays.stream(plusDM).sum();
        double smoothedMinusDM = Arrays.stream(minusDM).sum();

        if (smoothedTR == 0) {
            return 0;
        }

        // 计算DI
        double plusDI = 100 * smoothedPlusDM / smoothedTR;
        double minusDI = 100 * smoothedMinusDM / smoothedTR;

        // 计算DX
        if (plusDI + minusDI == 0) {
            return 0;
        }
        return 100 * Math.abs(plusDI - minusDI) / (plusDI + minusDI);
    }

    /**
     * 计算+DI指标
     */
    private double calculatePlusDI(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }

        double sumTR = 0;
        double sumPlusDM = 0;

        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;

            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);

            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();

            double tr = Math.max(Math.max(high - low, Math.abs(high - prevClose)), Math.abs(low - prevClose));
            sumTR += tr;

            double upMove = high - prev.getHigh().doubleValue();
            double downMove = prev.getLow().doubleValue() - low;

            if (upMove > downMove && upMove > 0) {
                sumPlusDM += upMove;
            }
        }

        return sumTR == 0 ? 0 : 100 * sumPlusDM / sumTR;
    }

    /**
     * 计算-DI指标
     */
    private double calculateMinusDI(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }

        double sumTR = 0;
        double sumMinusDM = 0;

        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;

            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);

            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();

            double tr = Math.max(Math.max(high - low, Math.abs(high - prevClose)), Math.abs(low - prevClose));
            sumTR += tr;

            double upMove = high - prev.getHigh().doubleValue();
            double downMove = prev.getLow().doubleValue() - low;

            if (downMove > upMove && downMove > 0) {
                sumMinusDM += downMove;
            }
        }

        return sumTR == 0 ? 0 : 100 * sumMinusDM / sumTR;
    }

    /**
     * 检测交易入场条件
     */
    private boolean checkHighWinRateConditions(
        List<KLineEntity> klineList,
        boolean isLong,
        BigDecimal close) {

        // 价格位置条件
        boolean pricePositionOK = isLong ?
            (close.doubleValue() < klineList.get(0).getMb()) :
            (close.doubleValue() > klineList.get(0).getMb());

        // 布林带趋势
        boolean bollBandCondition = isLong ?
            checkBollingerBandTrend(klineList, true) :
            checkBollingerBandTrend(klineList, false);

        // RSI条件
        boolean rsiOK = klineList.get(0).getRsi() > 30 && klineList.get(0).getRsi() < 70;

        // 成交量确认
        boolean volumeConfirm = klineList.get(0).getVolume() > calculateAverageVolume(klineList, 5);

        // 市场环境
        boolean marketValid = true;

        // 计算辅助条件得分
        int auxScore = 0;
        if (bollBandCondition) {
            auxScore++;
        }
        if (rsiOK) {
            auxScore++;
        }
        if (volumeConfirm) {
            auxScore++;
        }
        if (marketValid) {
            auxScore++;
        }

        // 条件组合逻辑：必须满足价格位置条件，且辅助条件至少满足2个
        return pricePositionOK && (auxScore >= 2);
    }

    /**
     * 判断布林带宽度趋势
     */
    private boolean checkBollingerBandTrend(List<KLineEntity> klineList, boolean isExpanding) {
        if (klineList.size() < 3) {
            return false;
        }

        double width0 = klineList.get(0).getUp() - klineList.get(0).getDn();
        double width1 = klineList.get(1).getUp() - klineList.get(1).getDn();
        double width2 = klineList.get(2).getUp() - klineList.get(2).getDn();

        return isExpanding ?
            (width0 > width1 && width1 > width2) :
            (width0 < width1 && width1 < width2);
    }

    /**
     * 交易结果记录类
     */
    @Data
    private static class TradeResult {
        private String instId;
        private String direction;
        private double entryPrice;
        private String posId;
        private double stopLossPrice;
        private double takeProfitPrice;
        private long entryTime;
    }
}
