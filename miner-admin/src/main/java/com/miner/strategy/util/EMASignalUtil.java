package com.miner.strategy.util;

import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EMA策略信号工具类
 * 提取核心信号实现为通用方法，便于回测使用
 */
@Slf4j
public class EMASignalUtil {

    /**
     * 检查EMA多头排列条件 (EMA9 > EMA21 > EMA55)
     * 要求当前K线必须是多头排列，而之前的4根K线不能全部是多头排列
     * @param klineList K线数据
     * @param emaFast 快速EMA周期 (默认9)
     * @param emaMid 中速EMA周期 (默认21)
     * @param emaSlow 慢速EMA周期 (默认55)
     * @return 是否满足多头排列
     */
    public static boolean checkEmaBullishAlignment(List<KLineEntity> klineList, 
                                                 int emaFast, int emaMid, int emaSlow) {
        // 确保有足够的数据检查当前K线和前4根K线
        if (klineList.size() < emaSlow + 5) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        // 检查当前K线是否多头排列
        double currentEmaFast = calculateEMA(closePrices, 0, emaFast);
        double currentEmaMid = calculateEMA(closePrices, 0, emaMid);
        double currentEmaSlow = calculateEMA(closePrices, 0, emaSlow);
        
        boolean currentIsAligned = currentEmaFast > currentEmaMid && currentEmaMid > currentEmaSlow;
        
        // 如果当前K线不是多头排列，直接返回false
        if (!currentIsAligned) {
            if (log.isDebugEnabled()) {
                log.debug("当前K线不满足EMA多头排列条件: EMA{}={}, EMA{}={}, EMA{}={}",
                    emaFast, String.format("%.4f", currentEmaFast),
                    emaMid, String.format("%.4f", currentEmaMid),
                    emaSlow, String.format("%.4f", currentEmaSlow));
            }
            return false;
        }
        
        // 检查前4根K线是否有不满足多头排列的情况
        boolean previousNotAllAligned = false;
        
        for (int i = 1; i <= 4; i++) {
            double emaFastValue = calculateEMA(closePrices, i, emaFast);
            double emaMidValue = calculateEMA(closePrices, i, emaMid);
            double emaSlowValue = calculateEMA(closePrices, i, emaSlow);
            
            boolean isAligned = emaFastValue > emaMidValue && emaMidValue > emaSlowValue;
            
            // 如果找到任意一根K线不满足多头排列，记录下来
            if (!isAligned) {
                previousNotAllAligned = true;
                if (log.isDebugEnabled()) {
                    log.debug("第{}根K线不满足EMA多头排列: EMA{}={}, EMA{}={}, EMA{}={}",
                        i, emaFast, String.format("%.4f", emaFastValue),
                        emaMid, String.format("%.4f", emaMidValue),
                        emaSlow, String.format("%.4f", emaSlowValue));
                }
                break;
            }
        }
        
        // 当前K线多头排列且之前4根K线不全是多头排列
        boolean result = currentIsAligned && previousNotAllAligned;
        
        if (log.isDebugEnabled()) {
            log.debug("EMA多头排列检查: 当前K线={}, 前4根不全是多头={}, 最终结果={}",
                currentIsAligned ? "满足" : "不满足",
                previousNotAllAligned ? "满足" : "不满足",
                result ? "满足" : "不满足");
        }

        return result;
    }

    /**
     * 通过小周期确认入场信号
     * @param klineList K线数据
     * @param rsiPeriod RSI周期 (默认14)
     * @param rsiOversold RSI超卖阈值 (默认30)
     * @param rsiRising RSI回升阈值 (默认40)
     * @param entryScoreThreshold 入场评分阈值 (默认5.0，优化后建议7.0)
     * @return 是否满足入场条件
     */
    public static boolean confirmEntrySignal(List<KLineEntity> klineList, 
                                           int rsiPeriod, double rsiOversold, 
                                           double rsiRising, double entryScoreThreshold) {
        // 检查市场状态，只在波动合理且趋势向上时交易
        if (!isLowVolatility(klineList, 14) || !isUptrend(klineList, 20)) {
            if (log.isDebugEnabled()) {
                log.debug("{} 市场状态不适合交易，拒绝入场", klineList.get(0).getInstId());
            }
            return false;
        }

        // 检查各指标的信号强度
        double bullishPatternStrength = checkBullishCandlePatternStrength(klineList);
        double rsiStrength = checkRsiStrength(klineList, rsiPeriod, rsiOversold, rsiRising);
        double macdStrength = checkMacdStrength(klineList);
        double volumeStrength = checkVolumeStrength(klineList);

        if (log.isDebugEnabled()) {
            log.debug("{} 小周期入场确认: 看涨K线形态={}, RSI={}, MACD信号={}, 成交量={}",
                klineList.get(0).getInstId(),
                String.format("%.2f", bullishPatternStrength),
                String.format("%.2f", rsiStrength),
                String.format("%.2f", macdStrength),
                String.format("%.2f", volumeStrength));
        }

        // 评分系统
        double totalScore = 0.0;
        double maxPossibleScore = 0.0;

        // 优化权重配置
        double candlePatternWeight = 1.5;
        double rsiWeight = 2.5;       // 增加RSI权重
        double macdWeight = 2.5;
        double volumeWeight = 2.0;    // 增加成交量权重

        // 计算加权分数
        totalScore += bullishPatternStrength * candlePatternWeight;
        totalScore += rsiStrength * rsiWeight;
        totalScore += macdStrength * macdWeight;
        totalScore += volumeStrength * volumeWeight;

        // 最大可能分数
        maxPossibleScore = candlePatternWeight + rsiWeight + macdWeight + volumeWeight;

        // 计算评分比例（满分10分）
        double scoreRatio = (totalScore / maxPossibleScore) * 10.0;

        // 修改为要求至少有两个强信号（超过0.7）
        int strongSignalCount = 0;
        if (macdStrength > 0.7) strongSignalCount++;
        if (rsiStrength > 0.7) strongSignalCount++;
        if (bullishPatternStrength > 0.7) strongSignalCount++;
        if (volumeStrength > 0.7) strongSignalCount++;
        
        boolean hasStrongSignals = strongSignalCount >= 2;

        if (log.isDebugEnabled()) {
            log.debug("{} 入场评分: {}/{} = {}/10, 阈值={}, 强信号数量={}",
                klineList.get(0).getInstId(),
                String.format("%.1f", totalScore),
                String.format("%.1f", maxPossibleScore),
                String.format("%.1f", scoreRatio),
                entryScoreThreshold,
                strongSignalCount);
        }

        return scoreRatio >= entryScoreThreshold && hasStrongSignals;
    }

    /**
     * 判断是否为上升趋势
     * 价格在MA均线上方且MA均线向上倾斜
     * @param klineList K线数据
     * @param period MA周期
     * @return 是否为上升趋势
     */
    public static boolean isUptrend(List<KLineEntity> klineList, int period) {
        if (klineList.size() < period + 10) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        // 计算当前移动平均线
        double ma = calculateMA(closePrices, 0, period);
        // 计算5周期前的移动平均线
        double maPrev = calculateMA(closePrices, 5, period);

        // 当前价格
        double currentPrice = closePrices.get(0);

        // 价格在均线上方且均线向上倾斜
        boolean priceAboveMA = currentPrice > ma;
        boolean maSloping = ma > maPrev;

        if (log.isDebugEnabled()) {
            log.debug("趋势检查: 当前价格={}, MA{}={}, 5周期前MA={}，价格>MA={}, MA上升={}",
                String.format("%.2f", currentPrice),
                period,
                String.format("%.2f", ma),
                String.format("%.2f", maPrev),
                priceAboveMA,
                maSloping);
        }

        return priceAboveMA && maSloping;
    }

    /**
     * 判断市场波动率是否适合交易
     * 避免在高波动市场中交易
     * @param klineList K线数据
     * @param period ATR周期
     * @return 是否为低波动率环境
     */
    public static boolean isLowVolatility(List<KLineEntity> klineList, int period) {
        if (klineList.size() < period + 1) {
            return false;
        }

        double atr = calculateATR(klineList, 0, period);
        double currentPrice = klineList.get(0).getClose().doubleValue();
        
        // 计算ATR占当前价格的百分比
        double atrPercent = atr / currentPrice * 100;

        boolean isLow = atrPercent < 3.0; // 波动率小于3%

        if (log.isDebugEnabled()) {
            log.debug("波动率检查: ATR={}, 价格={}, 波动率={}%, 判断结果={}",
                String.format("%.2f", atr),
                String.format("%.2f", currentPrice),
                String.format("%.2f", atrPercent),
                isLow ? "低波动" : "高波动");
        }

        return isLow;
    }

    /**
     * 计算移动平均线
     * @param prices 价格序列
     * @param offset 偏移量
     * @param period 周期
     * @return MA值
     */
    public static double calculateMA(List<Double> prices, int offset, int period) {
        if (prices.size() < period + offset) {
            return 0;
        }

        double sum = 0;
        for (int i = offset; i < offset + period; i++) {
            sum += prices.get(i);
        }

        return sum / period;
    }

    /**
     * 计算止盈止损价格
     * @param currentPrice 当前价格
     * @param klineList K线数据
     * @param atrPeriod ATR周期 (默认14)
     * @param stopLossMultiplier 止损倍数 (默认3，优化建议2)
     * @param takeProfitMultiplier1 止盈1倍数 (默认10)
     * @param takeProfitMultiplier2 止盈2倍数 (默认20)
     * @return 止盈止损价格对象
     */
    public static StopLevelDTO calculateStopLevels(BigDecimal currentPrice, 
                                               List<KLineEntity> klineList, 
                                               int atrPeriod,
                                               double stopLossMultiplier,
                                               double takeProfitMultiplier1,
                                               double takeProfitMultiplier2) {
        // 计算ATR用于设置止损和止盈
        double atr = calculateATR(klineList, 0, atrPeriod);
        if (atr <= 0) {
            log.warn("ATR计算错误，无法计算止盈止损");
            return null;
        }

        // 设置止损价 (指定倍数ATR)
        BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() - atr * stopLossMultiplier)
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 计算止盈价格（基于ATR倍数）
        BigDecimal takeProfitPrice1 = currentPrice.add(new BigDecimal(atr * takeProfitMultiplier1))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal takeProfitPrice2 = currentPrice.add(new BigDecimal(atr * takeProfitMultiplier2))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        return new StopLevelDTO(stopLossPrice, takeProfitPrice1, takeProfitPrice2);
    }

    /**
     * 检查K线形态强度 (0-1)
     */
    public static double checkBullishCandlePatternStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return 0.0;
        }

        // 检查多种K线形态并返回最强的信号
        double hammerStrength = checkHammerPatternStrength(klineList.get(0));
        double engulfingStrength = checkBullishEngulfingStrength(klineList);
        double morningStarStrength = checkMorningStarStrength(klineList);

        return Math.max(Math.max(hammerStrength, engulfingStrength), morningStarStrength);
    }

    /**
     * 检查锤子线形态强度
     */
    public static double checkHammerPatternStrength(KLineEntity kline) {
        double body = Math.abs(kline.getClose().doubleValue() - kline.getOpen().doubleValue());
        double lowerShadow = Math.min(kline.getOpen().doubleValue(), kline.getClose().doubleValue()) - kline.getLow().doubleValue();
        double upperShadow = kline.getHigh().doubleValue() - Math.max(kline.getOpen().doubleValue(), kline.getClose().doubleValue());
        double totalRange = kline.getHigh().doubleValue() - kline.getLow().doubleValue();

        if (totalRange == 0 || body == 0) return 0.0;

        // 锤子线特征
        if (lowerShadow > body * 2 && upperShadow < body * 0.5) {
            double shadowBodyRatio = lowerShadow / body;
            double bullishBonus = kline.getClose().doubleValue() > kline.getOpen().doubleValue() ? 0.2 : 0.0;
            return Math.min(0.5 + (shadowBodyRatio - 2.0) / 10.0 + bullishBonus, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查看涨吞没形态强度
     */
    public static double checkBullishEngulfingStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 2) return 0.0;

        KLineEntity previous = klineList.get(1);
        KLineEntity current = klineList.get(0);

        boolean previousIsBearish = previous.getClose().doubleValue() < previous.getOpen().doubleValue();
        boolean currentIsBullish = current.getClose().doubleValue() > current.getOpen().doubleValue();

        if (previousIsBearish && currentIsBullish) {
            boolean isEngulfing = current.getOpen().doubleValue() <= previous.getClose().doubleValue() &&
                                current.getClose().doubleValue() >= previous.getOpen().doubleValue();

            if (isEngulfing) {
                double prevBody = Math.abs(previous.getOpen().doubleValue() - previous.getClose().doubleValue());
                double currBody = Math.abs(current.getOpen().doubleValue() - current.getClose().doubleValue());
                double engulfingRatio = currBody / prevBody;
                
                return Math.min(0.6 + engulfingRatio * 0.2, 1.0);
            }
        }

        return 0.0;
    }

    /**
     * 检查启明星形态强度
     */
    public static double checkMorningStarStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) return 0.0;

        KLineEntity first = klineList.get(2);
        KLineEntity second = klineList.get(1);
        KLineEntity third = klineList.get(0);

        boolean firstIsBearish = first.getClose().doubleValue() < first.getOpen().doubleValue();
        double firstBodySize = Math.abs(first.getOpen().doubleValue() - first.getClose().doubleValue());
        double secondBodySize = Math.abs(second.getOpen().doubleValue() - second.getClose().doubleValue());
        boolean secondIsSmall = secondBodySize < firstBodySize * 0.5;
        boolean thirdIsBullish = third.getClose().doubleValue() > third.getOpen().doubleValue();

        if (firstIsBearish && secondIsSmall && thirdIsBullish) {
            double retracementRatio = 0.0;
            if (firstBodySize > 0) {
                double retracementAmount = third.getClose().doubleValue() - first.getClose().doubleValue();
                retracementRatio = Math.min(retracementAmount / firstBodySize, 1.0);
            }
            
            return Math.min(0.6 + retracementRatio * 0.4, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查RSI强度
     */
    public static double checkRsiStrength(List<KLineEntity> klineList, 
                                        int rsiPeriod, double rsiOversold, double rsiRising) {
        if (klineList.size() < rsiPeriod + 3) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        // 计算RSI值
        double currentRsi = calculateRSI(closePrices, 0, rsiPeriod);
        double previousRsi = calculateRSI(closePrices, 1, rsiPeriod);

        // 检查是否有过超卖
        boolean wasOversold = currentRsi < rsiOversold || previousRsi < rsiOversold;
        
        // 检查RSI上升趋势
        boolean isRising = currentRsi > previousRsi;

        if (wasOversold && isRising) {
            // RSI从超卖区回升的强度
            return Math.min((currentRsi - Math.min(previousRsi, rsiOversold)) / 20.0, 1.0);
        } else if (isRising && currentRsi > rsiRising) {
            // RSI上升但不在超卖区的强度较弱
            return Math.min((currentRsi - previousRsi) / 10.0, 0.5);
        }

        return 0.0;
    }

    /**
     * 检查MACD强度
     */
    public static double checkMacdStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 35) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        // 计算当前和前一个周期的MACD值
        double[] macd = calculateMACD(closePrices);
        double[] prevMacd = calculateMACD(closePrices.subList(1, closePrices.size()));

        double macdLine = macd[0];
        double signalLine = macd[1];
        double histogram = macd[2];
        double prevHistogram = prevMacd[2];

        // 金叉信号
        if (macdLine > signalLine && prevMacd[0] <= prevMacd[1]) {
            return 0.8; // 金叉信号较强
        }
        
        // 柱状图转正
        if (histogram > 0 && prevHistogram <= 0) {
            return 0.6; // 柱状图转正信号
        }
        
        // 柱状图扩大
        if (histogram > 0 && histogram > prevHistogram) {
            return 0.4; // 柱状图扩大信号较弱
        }

        return 0.0;
    }

    /**
     * 检查成交量强度
     */
    public static double checkVolumeStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 10) {
            return 0.0;
        }

        KLineEntity current = klineList.get(0);
        
        // 计算前9根K线的平均成交量
        double avgVolume = 0.0;
        for (int i = 1; i < 10; i++) {
            avgVolume += klineList.get(i).getVolume();
        }
        avgVolume /= 9.0;

        // 当前K线是否为阳线
        boolean currentIsBullish = current.getClose().doubleValue() > current.getOpen().doubleValue();

        if (currentIsBullish && avgVolume > 0) {
            // 计算成交量放大比例
            double volumeRatio = current.getVolume() / avgVolume;
            
            // 成交量要大于平均值才有效
            if (volumeRatio > 1.0) {
                return Math.min((volumeRatio - 1.0) / 2.0, 1.0);
            }
        }

        return 0.0;
    }

    /**
     * 计算EMA指标
     */
    public static double calculateEMA(List<Double> prices, int offset, int period) {
        if (prices.size() < offset + period) {
            return 0;
        }

        double alpha = 2.0 / (period + 1);
        double ema = prices.get(offset + period - 1);

        for (int i = offset + period - 2; i >= offset; i--) {
            ema = alpha * prices.get(i) + (1 - alpha) * ema;
        }

        return ema;
    }

    /**
     * 计算RSI指标
     */
    public static double calculateRSI(List<Double> prices, int offset, int period) {
        if (prices.size() < period + offset + 1) {
            return 50.0;
        }

        double sumGain = 0.0;
        double sumLoss = 0.0;

        for (int i = offset + 1; i <= offset + period; i++) {
            double change = prices.get(i - 1) - prices.get(i);
            if (change > 0) {
                sumGain += change;
            } else {
                sumLoss -= change;
            }
        }

        if (sumLoss == 0) {
            return 100.0;
        }

        double rs = sumGain / sumLoss;
        return 100.0 - (100.0 / (1.0 + rs));
    }

    /**
     * 计算ATR指标
     */
    public static double calculateATR(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }

        double[] trValues = new double[period];

        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;

            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);

            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            trValues[i] = Math.max(Math.max(tr1, tr2), tr3);
        }

        // 计算ATR
        double sum = 0;
        for (double tr : trValues) {
            sum += tr;
        }

        return sum / period;
    }

    /**
     * 计算MACD指标
     */
    public static double[] calculateMACD(List<Double> prices) {
        int fastLength = 12;
        int slowLength = 26;
        int signalLength = 9;

        // 计算快速EMA
        double fastEMA = calculateEMA(prices, 0, fastLength);

        // 计算慢速EMA
        double slowEMA = calculateEMA(prices, 0, slowLength);

        // 计算MACD线 (DIF)
        double macdLine = fastEMA - slowEMA;

        // 计算信号线 (DEA)
        List<Double> macdValues = new java.util.ArrayList<>();
        for (int i = 0; i < Math.min(signalLength, prices.size()); i++) {
            double fastEMAi = calculateEMA(prices, i, fastLength);
            double slowEMAi = calculateEMA(prices, i, slowLength);
            macdValues.add(fastEMAi - slowEMAi);
        }

        double signalLine = calculateEMA(macdValues, 0, signalLength);

        // 计算柱状图
        double histogram = macdLine - signalLine;

        return new double[]{macdLine, signalLine, histogram};
    }

    /**
     * 用于存储止盈止损价格的内部类
     */
    public static class StopLevelDTO {
        public final BigDecimal stopLossPrice;
        public final BigDecimal takeProfitPrice1;
        public final BigDecimal takeProfitPrice2;

        public StopLevelDTO(BigDecimal stopLossPrice, BigDecimal takeProfitPrice1, BigDecimal takeProfitPrice2) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice1 = takeProfitPrice1;
            this.takeProfitPrice2 = takeProfitPrice2;
        }
    }

    /**
     * 检测价格回调
     * 用于判断价格是否在上升趋势中出现回调
     * @param klineList K线数据
     * @param lookbackPeriod 回顾周期
     * @param pullbackPercent 回调百分比阈值
     * @return 是否出现回调
     */
    public static boolean detectPullback(List<KLineEntity> klineList, int lookbackPeriod, double pullbackPercent) {
        if (klineList.size() < lookbackPeriod + 1) {
            return false;
        }
        
        // 当前价格
        double currentPrice = klineList.get(0).getClose().doubleValue();
        
        // 寻找lookbackPeriod内的最高价
        double highestPrice = klineList.get(0).getHigh().doubleValue();
        for (int i = 1; i < lookbackPeriod; i++) {
            double high = klineList.get(i).getHigh().doubleValue();
            if (high > highestPrice) {
                highestPrice = high;
            }
        }
        
        // 计算当前价格相对于最高价的回调百分比
        double pullback = (highestPrice - currentPrice) / highestPrice * 100;
        
        // 判断是否在回调阈值范围内（例如3-10%之间的回调）
        boolean isPullback = pullback >= pullbackPercent && pullback <= pullbackPercent * 3;
        
        // 确保当前价格高于N周期前的价格（仍在上升趋势中）
        double priceNPeriodsAgo = klineList.get(lookbackPeriod).getClose().doubleValue();
        boolean stillUptrend = currentPrice > priceNPeriodsAgo;
        
        if (log.isDebugEnabled()) {
            log.debug("回调检测: 当前价格={}, 最高价={}, 回调幅度={}%, 趋势判断={}, 结果={}",
                String.format("%.2f", currentPrice),
                String.format("%.2f", highestPrice),
                String.format("%.2f", pullback),
                stillUptrend ? "上升" : "下降",
                (isPullback && stillUptrend) ? "满足" : "不满足");
        }
        
        return isPullback && stillUptrend;
    }

    /**
     * 检查价格是否处于支撑位附近
     * 支撑位定义为前N个周期的低点集中区域
     * @param klineList K线数据
     * @param period 周期
     * @param tolerance 容差百分比
     * @return 是否在支撑位附近
     */
    public static boolean isNearSupportLevel(List<KLineEntity> klineList, int period, double tolerance) {
        if (klineList.size() < period + 5) {
            return false;
        }
        
        // 当前价格
        double currentPrice = klineList.get(0).getClose().doubleValue();
        
        // 寻找过去N个周期的支撑位（取最低价的平均）
        List<Double> lows = new ArrayList<>();
        for (int i = 1; i < period; i++) {
            lows.add(klineList.get(i).getLow().doubleValue());
        }
        
        // 找出前三个最低点
        lows.sort(Double::compare);
        double supportLevel = 0;
        int count = Math.min(3, lows.size());
        for (int i = 0; i < count; i++) {
            supportLevel += lows.get(i);
        }
        supportLevel /= count;
        
        // 判断当前价格是否接近支撑位（在容差范围内）
        double distancePercent = Math.abs(currentPrice - supportLevel) / supportLevel * 100;
        boolean isNearSupport = distancePercent <= tolerance;
        
        if (log.isDebugEnabled()) {
            log.debug("支撑位检测: 当前价格={}, 支撑位={}, 距离={}%, 结果={}",
                String.format("%.2f", currentPrice),
                String.format("%.2f", supportLevel),
                String.format("%.2f", distancePercent),
                isNearSupport ? "接近支撑" : "远离支撑");
        }
        
        return isNearSupport;
    }
} 