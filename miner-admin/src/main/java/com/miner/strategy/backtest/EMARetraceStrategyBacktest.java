package com.miner.strategy.backtest;

import com.miner.strategy.backtest.analyzer.BacktestAnalyzer;
import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.engine.EMABacktestEngine;
import com.miner.strategy.backtest.loader.HistoricalDataLoader;
import com.miner.strategy.backtest.model.BacktestResult;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * EMA多头排列回调策略回测
 * 基于主周期EMA多头排列信号进行回测
 */
@Slf4j
@Component
public class EMARetraceStrategyBacktest {

    // EMA参数
    private static final int EMA_FAST = 9;
    private static final int EMA_MID = 21;
    private static final int EMA_SLOW = 55;

    // ATR参数
    private static final int ATR_PERIOD = 14;
    private static final double STOP_LOSS_ATR_MULTIPLIER = 1.5;
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_1 = 4;
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_2 = 10

        ;

    /**
     * 运行单次回测
     */
    public BacktestResult runBacktest(BacktestConfig config) {
        // 1. 加载历史数据
        Map<String, List<KLineEntity>> historicalData = HistoricalDataLoader.loadFromDatabase(config);

        if (historicalData.isEmpty()) {
            log.error("未能加载历史数据，回测中止");
            return null;
        }

        // 2. 创建回测引擎
        EMABacktestEngine engine = new EMABacktestEngine(config, historicalData);

        // 3. 运行回测
        log.info("开始运行回测...");
        BacktestResult result = engine.run();

        return result;
    }

    /**
     * 运行参数优化
     */
    public List<BacktestResult> runParameterOptimization(BacktestConfig baseConfig,
                                                       Map<String, List<Object>> paramRanges) {
        List<BacktestResult> results = Collections.synchronizedList(new ArrayList<>());

        // 加载历史数据
        Map<String, List<KLineEntity>> historicalData = HistoricalDataLoader.loadFromDatabase(baseConfig);

        if (historicalData.isEmpty()) {
            log.error("未能加载历史数据，参数优化中止");
            return results;
        }

        // 生成参数组合
        List<Map<String, Object>> paramCombinations = generateParameterCombinations(paramRanges);
        log.info("将测试 {} 组参数组合", paramCombinations.size());

        // 创建计数器
        AtomicInteger completedCount = new AtomicInteger(0);

        // 创建线程池
        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(processors + 1);
        List<Future<BacktestResult>> futures = new ArrayList<>();

        // 为每个参数组合创建任务
        for (Map<String, Object> params : paramCombinations) {
            futures.add(executor.submit(() -> {
                try {
                    BacktestConfig config = createConfigWithParams(baseConfig, params);
                    EMABacktestEngine engine = new EMABacktestEngine(config, historicalData);
                    BacktestResult result = engine.run();

                    int current = completedCount.incrementAndGet();
                    log.info("已完成 {}/{} 组参数测试 ({}%)",
                        current, paramCombinations.size(),
                        String.format("%.1f", (double) current / paramCombinations.size() * 100));

                    return result;
                } catch (Exception e) {
                    log.error("参数组合 {} 回测失败: {}", params, e.getMessage());
                    return null;
                }
            }));
        }

        // 收集结果
        for (Future<BacktestResult> future : futures) {
            try {
                BacktestResult result = future.get();
                if (result != null) {
                    results.add(result);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取回测结果失败: {}", e.getMessage());
            }
        }

        // 关闭线程池
        executor.shutdown();
        try {
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 分析结果
        analyzeResults(results);

        return results;
    }

    /**
     * 分析回测结果
     */
    private void analyzeResults(List<BacktestResult> results) {
        if (results.isEmpty()) {
            return;
        }

        // 按胜率排序
        results.sort((r1, r2) -> Double.compare(r2.getWinRate(), r1.getWinRate()));
        List<BacktestResult> winRateResults = results.subList(0, Math.min(3, results.size()));
        log.info("参数优化结果: ------胜率");
        // 按年化收益率排序
        results.sort((r1, r2) -> Double.compare(r2.getReturnRate(), r1.getReturnRate()));
        List<BacktestResult> returnRateResults = results.subList(0, Math.min(3, results.size()));
        log.info("参数优化结果: ------收益率");
        BacktestAnalyzer.generateParameterOptimizationTable(returnRateResults);
        BacktestAnalyzer.generateParameterOptimizationTable(winRateResults);
    }

    /**
     * 生成参数组合
     */
    private List<Map<String, Object>> generateParameterCombinations(Map<String, List<Object>> paramRanges) {
        List<Map<String, Object>> combinations = new ArrayList<>();
        generateCombinations(paramRanges, new LinkedHashMap<>(),
            new ArrayList<>(paramRanges.keySet()), 0, combinations);
        return combinations;
    }

    /**
     * 递归生成参数组合
     */
    private void generateCombinations(Map<String, List<Object>> paramRanges,
                                    Map<String, Object> current,
                                    List<String> paramNames,
                                    int index,
                                    List<Map<String, Object>> combinations) {
        if (index >= paramNames.size()) {
            combinations.add(new LinkedHashMap<>(current));
            return;
        }

        String paramName = paramNames.get(index);
        List<Object> values = paramRanges.get(paramName);

        for (Object value : values) {
            current.put(paramName, value);
            generateCombinations(paramRanges, current, paramNames, index + 1, combinations);
        }
    }

    /**
     * 创建带有特定参数的配置
     */
    private BacktestConfig createConfigWithParams(BacktestConfig baseConfig,
                                                Map<String, Object> params) {
        BacktestConfig.BacktestConfigBuilder builder = BacktestConfig.builder()
            .startDate(baseConfig.getStartDate())
            .endDate(baseConfig.getEndDate())
            .symbols(baseConfig.getSymbols())
            .initialCapital(baseConfig.getInitialCapital())
            .positionSizeRatio(baseConfig.getPositionSizeRatio())
            .mainTimeframe(baseConfig.getMainTimeframe())
            .subTimeframe(baseConfig.getSubTimeframe())
            .tradeFeeRate(baseConfig.getTradeFeeRate())
            .slippageRatio(baseConfig.getSlippageRatio());

        // 设置参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            switch (entry.getKey()) {
                case "emaFast":
                    builder.emaFast((Integer) entry.getValue());
                    break;
                case "emaMid":
                    builder.emaMid((Integer) entry.getValue());
                    break;
                case "emaSlow":
                    builder.emaSlow((Integer) entry.getValue());
                    break;
                case "atrPeriod":
                    builder.atrPeriod((Integer) entry.getValue());
                    break;
                case "stopLossMultiplier":
                    builder.stopLossMultiplier((Double) entry.getValue());
                    break;
                case "takeProfitMultiplier1":
                    builder.takeProfitMultiplier1((Double) entry.getValue());
                    break;
                case "takeProfitMultiplier2":
                    builder.takeProfitMultiplier2((Double) entry.getValue());
                    break;
                default:
                    log.warn("未知参数: {}", entry.getKey());
            }
        }

        // 设置默认值
        if (!params.containsKey("emaFast")) builder.emaFast(EMA_FAST);
        if (!params.containsKey("emaMid")) builder.emaMid(EMA_MID);
        if (!params.containsKey("emaSlow")) builder.emaSlow(EMA_SLOW);
        if (!params.containsKey("atrPeriod")) builder.atrPeriod(ATR_PERIOD);
        if (!params.containsKey("stopLossMultiplier")) builder.stopLossMultiplier(STOP_LOSS_ATR_MULTIPLIER);
        if (!params.containsKey("takeProfitMultiplier1")) builder.takeProfitMultiplier1(TAKE_PROFIT_ATR_MULTIPLIER_1);
        if (!params.containsKey("takeProfitMultiplier2")) builder.takeProfitMultiplier2(TAKE_PROFIT_ATR_MULTIPLIER_2);

        return builder.build();
    }

    /**
     * 运行单次回测示例
     */
    public void runSingleBacktest() {
        // 创建回测配置
        BacktestConfig config = BacktestConfig.builder()
            .startDate(LocalDateTime.now().minusMonths(12))  // 回测12个月
            .endDate(LocalDateTime.now())
            .symbols(Arrays.asList("BTC-USDT-SWAP"))  // 测试BTC和ETH
            .initialCapital(10000.0)  // 初始资金1万USDT
            .positionSizeRatio(1)  // 每次开仓2%
            .mainTimeframe("4h")  // 主周期4小时
            .subTimeframe("15m")  // 子周期15分钟
            .emaFast(EMA_FAST)  // EMA9
            .emaMid(EMA_MID)  // EMA21
            .emaSlow(EMA_SLOW)  // EMA55
            .atrPeriod(ATR_PERIOD)  // ATR周期14
            .stopLossMultiplier(STOP_LOSS_ATR_MULTIPLIER)  // 止损3倍ATR
            .takeProfitMultiplier1(TAKE_PROFIT_ATR_MULTIPLIER_1)  // 第一止盈10倍ATR
            .takeProfitMultiplier2(TAKE_PROFIT_ATR_MULTIPLIER_2)  // 第二止盈25倍ATR
            .build();

        // 运行回测
        BacktestResult result = runBacktest(config);

//        if (result != null) {
//            log.info("回测结果总结:\n{}", result.getSummary());
//        }
    }

    /**
     * 运行参数优化示例
     */
    public void runParameterOptimizationExample() {
        // 创建基础配置
        BacktestConfig baseConfig = BacktestConfig.builder()
            .startDate(LocalDateTime.now().minusMonths(6))
            .endDate(LocalDateTime.now())
            .symbols(Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP"))
            .initialCapital(10000.0)
            .positionSizeRatio(0.02)
            .mainTimeframe("4h")
            .subTimeframe("5m")
            .tradeFeeRate(0.001)
            .slippageRatio(0.001)
            .build();

        // 定义参数优化范围
        Map<String, List<Object>> paramRanges = new LinkedHashMap<>();
        paramRanges.put("emaFast", Arrays.asList(9, 12, 14));
        paramRanges.put("emaMid", Arrays.asList(21, 26, 30));
        paramRanges.put("emaSlow", Arrays.asList(50, 55, 60));
        paramRanges.put("stopLossMultiplier", Arrays.asList(2.0, 3.0, 4.0));
        paramRanges.put("takeProfitMultiplier1", Arrays.asList(8.0, 10.0, 12.0));
        paramRanges.put("takeProfitMultiplier2", Arrays.asList(16.0, 18.0, 20.0));

        // 运行参数优化
        List<BacktestResult> results = runParameterOptimization(baseConfig, paramRanges);

        // 输出最佳参数组合
        if (!results.isEmpty()) {
            BacktestResult bestResult = results.get(0);
            log.info("最佳参数组合: {}", bestResult.getParameterSettings());
            log.info("最佳结果: {}", bestResult.getSummary());
        }
    }

    public static void main(String[] args) {
        EMARetraceStrategyBacktest backtest = new EMARetraceStrategyBacktest();
        backtest.runSingleBacktest();
    }
}
