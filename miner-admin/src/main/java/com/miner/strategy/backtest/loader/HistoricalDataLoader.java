package com.miner.strategy.backtest.loader;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.system.okx.service.marketData.MarketDataAPIService;
import com.miner.system.okx.service.marketData.impl.MarketDataAPIServiceImpl;
import com.miner.system.okx.service.publicData.PublicDataAPIService;
import com.miner.system.okx.service.publicData.impl.PublicDataAPIServiceImpl;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.collection.CollUtil;
import com.miner.system.domain.Bar;
import com.miner.system.mapper.BarMapper;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 历史数据加载器
 * 用于从不同来源加载历史K线数据
 */
@Slf4j
@Component
public class HistoricalDataLoader implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        HistoricalDataLoader.applicationContext = applicationContext;
    }

    /**
     * 从CSV文件加载历史数据
     * 典型的CSV格式: symbol,timestamp,open,high,low,close,volume,timeframe
     * 例如: BTC-USDT,1648483200,47100.5,47350.2,46800.3,47120.8,1234.5,4h
     *
     * @param csvFilePath CSV文件路径
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromCsv(String csvFilePath, BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(csvFilePath))) {
            String line;
            boolean isHeader = true;

            while ((line = reader.readLine()) != null) {
                if (isHeader) {
                    isHeader = false;
                    continue;
                }

                String[] fields = line.split(",");
                if (fields.length < 8) {
                    log.warn("忽略无效数据行: {}", line);
                    continue;
                }

                String symbol = fields[0];
                long timestamp = Long.parseLong(fields[1]);
                double open = Double.parseDouble(fields[2]);
                double high = Double.parseDouble(fields[3]);
                double low = Double.parseDouble(fields[4]);
                double close = Double.parseDouble(fields[5]);
                float volume = Float.parseFloat(fields[6]);
                String timeframe = fields[7];

                // 过滤交易对
                if (config.getSymbols() != null && !config.getSymbols().isEmpty()) {
                    if (!config.getSymbols().contains(symbol)) {
                        continue;
                    }
                }

                // 过滤K线周期
                if (!timeframe.equals(config.getMainTimeframe()) && !timeframe.equals(config.getSubTimeframe())) {
                    continue;
                }

                // 转换为LocalDateTime
                LocalDateTime dateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

                // 过滤日期范围
                if (config.getStartDate() != null && dateTime.isBefore(config.getStartDate())) {
                    continue;
                }
                if (config.getEndDate() != null && dateTime.isAfter(config.getEndDate())) {
                    continue;
                }

                // 创建KLineEntity对象
                KLineEntity kline = new KLineEntity();
                kline.setInstId(symbol);
                kline.setTs(timestamp);
                kline.setOpen(BigDecimal.valueOf(open));
                kline.setHigh(BigDecimal.valueOf(high));
                kline.setLow(BigDecimal.valueOf(low));
                kline.setClose(BigDecimal.valueOf(close));
                kline.setVolume(volume);
                kline.setBar(timeframe);

                // 添加到对应的交易对列表
                historicalData
                    .computeIfAbsent(symbol, k -> new ArrayList<>())
                    .add(kline);
            }

            // 对每个交易对的K线数据按时间排序（最新的排在前面）
            for (List<KLineEntity> klines : historicalData.values()) {
                klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));
            }

            log.info("从CSV加载了{}个交易对的历史数据", historicalData.size());

        } catch (IOException e) {
            log.error("加载CSV历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 从数据库加载历史数据
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromDatabase(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        try {
            if (applicationContext == null) {
                log.error("应用上下文未初始化，无法获取BarMapper");
                return historicalData;
            }

            BarMapper barMapper = applicationContext.getBean(BarMapper.class);
            if (barMapper == null) {
                log.error("无法获取BarMapper实例");
                return historicalData;
            }

            // 获取交易对列表
            List<String> symbols = config.getSymbols();
            if (symbols == null || symbols.isEmpty()) {
                symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
                log.info("未指定交易对，使用默认交易对：{}", symbols);
            }

            // 时间格式化
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 时间范围
            String startDate = config.getStartDate() != null ?
                config.getStartDate().format(formatter) :
                LocalDateTime.now().minusDays(30).format(formatter);

            String endDate = config.getEndDate() != null ?
                config.getEndDate().format(formatter) :
                LocalDateTime.now().format(formatter);

            log.info("从数据库加载历史数据，时间范围: {} 至 {}", startDate, endDate);

            // 获取K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe().toUpperCase());
            if (!config.getMainTimeframe().equalsIgnoreCase(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe().toUpperCase());
            }

            // 使用并行流处理多个交易对
            symbols.parallelStream().forEach(symbol -> {
                List<KLineEntity> symbolKlines = new ArrayList<>();

                for (String timeframe : timeframes) {
                    log.info("正在加载 {} 的 {} 周期数据...", symbol, timeframe);

                    // 批量获取数据
                    List<Bar> bars = barMapper.getBars(symbol, timeframe, startDate, endDate);
                    if (bars == null || bars.isEmpty()) {
                        log.warn("未找到 {} 的 {} 周期数据", symbol, timeframe);
                        continue;
                    }

                    // 使用并行流转换数据
                    List<KLineEntity> klines = bars.parallelStream()
                        .map(bar -> {
                            KLineEntity kline = new KLineEntity();
                            kline.setInstId(bar.getInstId());
                            kline.setTs(bar.getTs());
                            kline.setOpen(new BigDecimal(bar.getOpen()));
                            kline.setHigh(new BigDecimal(bar.getHigh()));
                            kline.setLow(new BigDecimal(bar.getLow()));
                            kline.setClose(new BigDecimal(bar.getClose()));
                            kline.setVolume(bar.getVol().floatValue());
                            kline.setBar(timeframe.toLowerCase());
                            kline.setDate(DateUtil.formatDateTime(bar.getDate()));
                            return kline;
                        })
                        .collect(Collectors.toList());

                    symbolKlines.addAll(klines);
                    log.info("成功加载 {} 的 {} 周期数据，共 {} 条", symbol, timeframe, klines.size());
                }

                // 按时间戳降序排序
                symbolKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                // 使用线程安全的方式添加到结果中
                synchronized (historicalData) {
                    if (!symbolKlines.isEmpty()) {
                        historicalData.put(symbol, symbolKlines);
                    }
                }
            });

            log.info("从数据库加载了 {} 个交易对的历史数据", historicalData.size());

        } catch (Exception e) {
            log.error("从数据库加载历史数据失败: {}", e.getMessage(), e);
        }

        return historicalData;
    }

    /**
     * 使用API从交易所加载历史数据
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromExchange(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        // 初始化交易所API服务
        PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();
        MarketDataAPIService marketDataAPIService = new MarketDataAPIServiceImpl();

        try {
            // 获取交易对列表
            List<String> symbols;
            if (config.getSymbols() != null && !config.getSymbols().isEmpty()) {
                symbols = config.getSymbols();
            } else {
                symbols = getInstIds(publicDataAPIService, marketDataAPIService, 20);
            }

            // 确定时间范围
            long startTimestamp = config.getStartDate() != null ?
                config.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().minus(30, ChronoUnit.DAYS).toEpochMilli();

            long endTimestamp = config.getEndDate() != null ?
                config.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().toEpochMilli();
            log.info("从 {} 到 {} 的时间范围中获取历史数据",
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), ZoneId.systemDefault()),
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), ZoneId.systemDefault()));
            // 获取所需的K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe());
            if (!config.getMainTimeframe().equals(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe());
            }
            log.info("获取的K线周期为: {}", JSON.toJSONString(timeframes));

            // 为每个交易对抓取数据
            for (String symbol : symbols) {
                log.info("正在从交易所获取 {} 的历史数据", symbol);

                for (String timeframe : timeframes) {
                    // 将时间周期转换为OKX API格式
                    String okxBar = convertTimeframeToOkxFormat(timeframe);

                    // 按时间段分批获取数据
                    long step = getStepSize(timeframe);
                    List<KLineEntity> klines = new ArrayList<>();

                    for (long timestamp = startTimestamp; timestamp < endTimestamp; timestamp += step) {
                        // 计算结束时间点，不超过当前设置的结束时间
                        long batchEndTs = Math.min(timestamp + step, endTimestamp);
//                        log.info("正在获取 {} 的 {} 周期的数据，时间范围: {} - {}", symbol, timeframe,
//                            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()),
//                            LocalDateTime.ofInstant(Instant.ofEpochMilli(batchEndTs), ZoneId.systemDefault()));
                        // 调用OKX API获取K线数据
                        JSONObject barJson = marketDataAPIService.getCandlesticksHistory(
                            symbol,
                            String.valueOf(batchEndTs),
                            String.valueOf(timestamp),
                            okxBar,
                            "100"  // 每次最多获取300条数据
                        );

                        // 解析K线数据
                        List<KLineEntity> batchKlines = parseKlines(barJson, symbol, timeframe);
                        klines.addAll(batchKlines);

                        // 避免频繁请求触发限制
                        ThreadUtil.sleep(50);
                    }

                    // 添加到结果中
                    if (!klines.isEmpty()) {
                        // 按时间戳排序（降序）
                        klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                        // 合并到历史数据中
                        List<KLineEntity> existingKlines = historicalData.computeIfAbsent(symbol, k -> new ArrayList<>());
                        existingKlines.addAll(klines);
                    }

                    log.info("{} {} 数据获取成功，共{}条", symbol, timeframe, klines.size());
                }
            }

        } catch (Exception e) {
            log.error("从交易所加载历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 解析K线数据
     */
    private static List<KLineEntity> parseKlines(JSONObject barJson, String symbol, String timeframe) {
        List<KLineEntity> klines = new ArrayList<>();

        if (barJson == null || !barJson.containsKey("data")) {
            return klines;
        }

        JSONArray dataArray = barJson.getJSONArray("data");
        if (dataArray == null) {
            return klines;
        }

        for (int i = 0; i < dataArray.size(); i++) {
            JSONArray item = dataArray.getJSONArray(i);
            if (item == null || item.size() < 8) {
                continue;
            }

            try {
                // 解析K线数据
                long ts = item.getLong(0);  // 开始时间，Unix时间戳的毫秒数格式
                String open = item.getString(1);  // 开盘价格
                String high = item.getString(2);  // 最高价格
                String low = item.getString(3);   // 最低价格
                String close = item.getString(4); // 收盘价格
                BigDecimal volCcy = item.getBigDecimal(7); // 交易量（按币种计算）

                // 创建KLineEntity对象
                KLineEntity kline = new KLineEntity();
                kline.setInstId(symbol);
                kline.setTs(ts / 1000); // 转换为秒
                kline.setOpen(new BigDecimal(open));
                kline.setHigh(new BigDecimal(high));
                kline.setLow(new BigDecimal(low));
                kline.setClose(new BigDecimal(close));
                kline.setVolume(volCcy != null ? volCcy.floatValue() : 0f);
                kline.setBar(timeframe);

                klines.add(kline);
            } catch (Exception e) {
                log.warn("解析K线数据失败: {}", e.getMessage());
            }
        }

        return klines;
    }

    /**
     * 获取交易对列表
     */
    private static List<String> getInstIds(PublicDataAPIService publicDataAPIService,
                                        MarketDataAPIService marketDataAPIService,
                                        int size) {
        Map<String, Double> volumeMap = new HashMap<>();
        JSONObject instruments = publicDataAPIService.getInstruments("SWAP", null, null);

        if (instruments == null || !instruments.containsKey("data")) {
            log.error("获取交易对列表失败");
            return new ArrayList<>();
        }

        final JSONArray jsonArray = instruments.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            final JSONObject object = jsonArray.getJSONObject(i);
            final String instId = object.getString("instId");
            if (!instId.contains("USDT")) {
                continue;
            }

            try {
                final double volume = getVolume(marketDataAPIService, instId);
                volumeMap.put(instId, volume);
            } catch (Exception e) {
                log.warn("获取{}交易量失败: {}", instId, e.getMessage());
            }

            ThreadUtil.sleep(100);
        }

        // 按交易量排序并返回前N个
        return volumeMap.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .limit(size)
            .map(Map.Entry::getKey)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取交易对的成交量
     */
    private static double getVolume(MarketDataAPIService marketDataAPIService, String instId) {
        JSONObject tickers = marketDataAPIService.getTicker(instId);
        if (tickers == null || !tickers.containsKey("data") || tickers.getJSONArray("data").isEmpty()) {
            return 0.0;
        }

        JSONObject data = tickers.getJSONArray("data").getJSONObject(0);
        final Double last = data.getDouble("last");
        final Double vol24h = data.getDouble("volCcy24h");
        return (last != null && vol24h != null) ? last * vol24h : 0.0;
    }

    /**
     * 根据K线周期计算时间步长（单位：毫秒）
     */
    private static long getStepSize(String timeframe) {
        // 将时间周期转换为毫秒数，再乘以300（每次获取300根K线）
        return getIntervalSeconds(timeframe) * 100L;
    }

    /**
     * 将回测系统的时间周期格式转换为OKX API格式
     */
    private static String convertTimeframeToOkxFormat(String timeframe) {
        // 1m, 5m, 15m -> 1m, 5m, 15m
        // 30m -> 30m
        // 1h, 4h -> 1H, 4H
        // 1d -> 1D
        if (timeframe.endsWith("h")) {
            return timeframe.substring(0, timeframe.length() - 1) + "H";
        } else if (timeframe.endsWith("d")) {
            return timeframe.substring(0, timeframe.length() - 1) + "D";
        }
        return timeframe;
    }

    /**
     * 创建模拟数据用于测试
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> createMockData(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        // 默认交易对
        List<String> symbols = config.getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
        }

        // 默认日期范围
        LocalDateTime startDate = config.getStartDate();
        LocalDateTime endDate = config.getEndDate();
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        // 生成模拟数据
        for (String symbol : symbols) {
            List<KLineEntity> mainTimeframeKlines = generateMockKlines(
                symbol, startDate, endDate, config.getMainTimeframe(), 100);
            List<KLineEntity> subTimeframeKlines = generateMockKlines(
                symbol, startDate, endDate, config.getSubTimeframe(), 2000);

            List<KLineEntity> combinedKlines = new ArrayList<>();
            combinedKlines.addAll(mainTimeframeKlines);
            combinedKlines.addAll(subTimeframeKlines);

            historicalData.put(symbol, combinedKlines);
        }

        log.info("生成了{}个交易对的模拟历史数据", historicalData.size());
        return historicalData;
    }

    /**
     * 生成模拟K线数据
     */
    private static List<KLineEntity> generateMockKlines(String symbol, LocalDateTime startDate,
                                                      LocalDateTime endDate, String timeframe,
                                                      int approxCount) {
        List<KLineEntity> klines = new ArrayList<>();

        // 确定K线间隔（秒）
        int intervalSeconds = getIntervalSeconds(timeframe);

        // 生成K线
        Random random = new Random(symbol.hashCode()); // 使用固定种子保证每次生成相同数据
        double basePrice = 100 + random.nextDouble() * 1000; // 基础价格
        double lastClose = basePrice;

        // 计算总K线数量和间隔
        long startEpoch = startDate.atZone(ZoneId.systemDefault()).toEpochSecond();
        long endEpoch = endDate.atZone(ZoneId.systemDefault()).toEpochSecond();
        long totalSeconds = endEpoch - startEpoch;
        int step = (int) (totalSeconds / approxCount);
        step = Math.max(step, intervalSeconds);

        // 生成K线
        for (long ts = startEpoch; ts <= endEpoch; ts += step) {
            // 生成价格波动，确保有一些趋势和随机性
            double range = lastClose * 0.02; // 2%波动范围
            double change = (random.nextDouble() - 0.5) * range;

            // 添加一些趋势
            if (random.nextDouble() > 0.7) {
                // 30%的概率有较强的趋势
                change += range * (random.nextDouble() - 0.3) * 2;
            }

            double open = lastClose;
            double close = open + change;

            // 确保价格为正
            close = Math.max(close, open * 0.5);

            // 确定高低点
            double max = Math.max(open, close);
            double min = Math.min(open, close);
            double high = max + random.nextDouble() * (max - min) * 0.5;
            double low = min - random.nextDouble() * (max - min) * 0.5;

            // 生成成交量
            double volume = lastClose * (10 + random.nextDouble() * 90);

            // 创建K线对象
            KLineEntity kline = new KLineEntity();
            kline.setInstId(symbol);
            kline.setTs(ts);
            kline.setOpen(BigDecimal.valueOf(open));
            kline.setHigh(BigDecimal.valueOf(high));
            kline.setLow(BigDecimal.valueOf(low));
            kline.setClose(BigDecimal.valueOf(close));
            kline.setVolume(new Double(volume).floatValue());
            kline.setBar(timeframe);

            klines.add(kline);
            lastClose = close;
        }

        // 排序（按时间降序）
        klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

        return klines;
    }

    /**
     * 将时间周期转换为毫秒数
     */
    private static int getIntervalSeconds(String timeframe) {
        // 例如：1m, 5m, 15m, 30m, 1h, 4h, 1d
        int multiplier = Integer.parseInt(timeframe.substring(0, timeframe.length() - 1));
        char unit = timeframe.charAt(timeframe.length() - 1);

        switch (unit) {
            case 'm': return multiplier * 60000;
            case 'h':  return multiplier * 60 * 60000;
            case 'd': return multiplier * 24 * 60 * 60000;
            default: return 60; // 默认1分钟
        }
    }
}
