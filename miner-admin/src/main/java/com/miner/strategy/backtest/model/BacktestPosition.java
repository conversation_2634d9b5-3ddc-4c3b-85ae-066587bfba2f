package com.miner.strategy.backtest.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 回测持仓模型
 * 用于记录回测过程中的交易状态
 */
@Data
@Builder
public class BacktestPosition {
    // 交易对
    private String symbol;
    
    // 持仓ID
    private String positionId;
    
    // 开仓时间
    private LocalDateTime entryTime;
    
    // 开仓价格
    private BigDecimal entryPrice;
    
    // 持仓数量
    private BigDecimal positionSize;
    
    // 止损价格
    private BigDecimal stopLossPrice;
    
    // 止盈价格1（50%仓位）
    private BigDecimal takeProfit1Price;
    
    // 止盈价格2（剩余仓位）
    private BigDecimal takeProfit2Price;
    
    // 是否已经平仓50%
    private boolean partialClosed;
    
    // 开仓手续费
    private BigDecimal entryFee;
    
    // 用于跟踪止损的最高价
    private BigDecimal highestPrice;
    
    // 平仓信息
    private LocalDateTime closeTime;
    private BigDecimal closePrice;
    private BigDecimal closingFee;
    private String closeReason; // "stop_loss", "take_profit_1", "take_profit_2", "manual", "trailing_stop"
    
    // 交易盈亏
    private BigDecimal pnl;
    private BigDecimal pnlRatio; // 盈亏比例
    
    // 持仓状态
    private PositionStatus status;
    
    /**
     * 持仓状态枚举
     */
    public enum PositionStatus {
        OPEN,           // 开仓状态
        PARTIAL_CLOSED, // 部分平仓（止盈1已触发）
        CLOSED          // 已平仓
    }
    
    /**
     * 计算当前持仓的未实现盈亏
     * @param currentPrice 当前价格
     * @return 未实现盈亏
     */
    public BigDecimal calculateUnrealizedPnl(BigDecimal currentPrice) {
        if (status == PositionStatus.CLOSED) {
            return pnl;
        }
        
        BigDecimal priceDiff = currentPrice.subtract(entryPrice);
        BigDecimal unrealizedPnl = priceDiff.multiply(positionSize);
        
        // 如果已经部分平仓，需要考虑已实现的盈亏
        if (status == PositionStatus.PARTIAL_CLOSED && pnl != null) {
            unrealizedPnl = unrealizedPnl.add(pnl);
        }
        
        return unrealizedPnl;
    }
} 