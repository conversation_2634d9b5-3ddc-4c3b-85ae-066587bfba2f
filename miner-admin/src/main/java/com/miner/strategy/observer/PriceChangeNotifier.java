package com.miner.strategy.observer;

import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 价格变化通知器
 * 管理所有价格变化观察者，并在价格变化时通知相关观察者
 */
@Component
@Slf4j
public class PriceChangeNotifier {

    // 观察者列表
    private final List<PriceChangeObserver> observers = new CopyOnWriteArrayList<>();

    // 价格缓存，用于检测价格变化
    private final ConcurrentHashMap<String, Double> priceCache = new ConcurrentHashMap<>();

    // 异步通知线程池
    private final ExecutorService notificationExecutor = Executors.newFixedThreadPool(5);

    /**
     * 注册观察者
     */
    public void registerObserver(PriceChangeObserver observer) {
        if (!observers.contains(observer)) {
            observers.add(observer);
            log.info("注册价格变化观察者: {}", observer.getObserverName());
        }
    }

    /**
     * 移除观察者
     */
    public void removeObserver(PriceChangeObserver observer) {
        if (observers.remove(observer)) {
            log.info("移除价格变化观察者: {}", observer.getObserverName());
        }
    }

    /**
     * 通知价格变化
     */
    public void notifyPriceChange(String instId, KLineEntity klineData) {
        if (klineData == null || klineData.getClose() == null) {
            return;
        }

        double newPrice = klineData.getClose().doubleValue();
        Double oldPrice = priceCache.put(instId, newPrice);

        // 如果是第一次记录价格，不触发通知
        if (oldPrice == null) {
            return;
        }

        // 如果价格没有变化，不触发通知
        if (Math.abs(newPrice - oldPrice) < 0.0001) {
            return;
        }

        // 异步通知所有感兴趣的观察者
        notificationExecutor.submit(() -> {
            for (PriceChangeObserver observer : observers) {
                try {
                    if (observer.isInterestedIn(instId)) {
                        observer.onPriceChange(instId, oldPrice, newPrice, klineData);
                    }
                } catch (Exception e) {
                    log.error("观察者 {} 处理价格变化事件时发生异常: {}",
                        observer.getObserverName(), e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 批量通知价格变化
     */
    public void notifyPriceChanges(List<KLineEntity> klineDataList) {
        if (klineDataList == null || klineDataList.isEmpty()) {
            return;
        }

        for (KLineEntity klineData : klineDataList) {
            notifyPriceChange(klineData.getInstId(), klineData);
        }
    }

    /**
     * 获取当前价格
     */
    public Double getCurrentPrice(String instId) {
        return priceCache.get(instId);
    }

    /**
     * 获取观察者数量
     */
    public int getObserverCount() {
        return observers.size();
    }

    /**
     * 获取缓存的交易对数量
     */
    public int getCachedPairCount() {
        return priceCache.size();
    }

    /**
     * 清理价格缓存
     */
    public void clearPriceCache() {
        priceCache.clear();
        log.info("已清理价格缓存");
    }

    /**
     * 关闭通知器
     */
    public void shutdown() {
        notificationExecutor.shutdown();
        log.info("价格变化通知器已关闭");
    }
}
