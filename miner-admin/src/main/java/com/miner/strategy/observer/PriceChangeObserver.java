package com.miner.strategy.observer;


import com.miner.system.indicator.KLineEntity;

/**
 * 价格变化观察者接口
 * 使用观察者模式监听价格变化事件
 */
public interface PriceChangeObserver {

    /**
     * 处理价格变化事件
     * @param instId 交易对ID
     * @param oldPrice 旧价格
     * @param newPrice 新价格
     * @param klineData 最新K线数据
     */
    void onPriceChange(String instId, double oldPrice, double newPrice, KLineEntity klineData);

    /**
     * 获取观察者名称
     */
    String getObserverName();

    /**
     * 是否对该交易对感兴趣
     */
    boolean isInterestedIn(String instId);
}
