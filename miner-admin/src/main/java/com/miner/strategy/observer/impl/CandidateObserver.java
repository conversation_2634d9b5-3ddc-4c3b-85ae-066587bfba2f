package com.miner.strategy.observer.impl;

import com.miner.strategy.observer.PriceChangeObserver;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 候选交易对观察者
 * 监听候选交易对的价格变化，触发相应的策略检查
 */
@Component
@Slf4j
public class CandidateObserver implements PriceChangeObserver {

    // 候选交易对集合
    private final Set<String> candidateInstIds = ConcurrentHashMap.newKeySet();

    // 做空候选交易对集合
    private final Set<String> shortCandidateInstIds = ConcurrentHashMap.newKeySet();

    @Override
    public void onPriceChange(String instId, double oldPrice, double newPrice, KLineEntity klineData) {
        // 计算价格变化百分比
        double changePercent = ((newPrice - oldPrice) / oldPrice) * 100;

        if (candidateInstIds.contains(instId)) {
            handleLongCandidatePriceChange(instId, oldPrice, newPrice, changePercent, klineData);
        }

        if (shortCandidateInstIds.contains(instId)) {
            handleShortCandidatePriceChange(instId, oldPrice, newPrice, changePercent, klineData);
        }
    }

    @Override
    public String getObserverName() {
        return "CandidateObserver";
    }

    @Override
    public boolean isInterestedIn(String instId) {
        return candidateInstIds.contains(instId) || shortCandidateInstIds.contains(instId);
    }

    /**
     * 添加做多候选交易对
     */
    public void addLongCandidate(String instId) {
        if (candidateInstIds.add(instId)) {
            log.debug("添加做多候选交易对: {}", instId);
        }
    }

    /**
     * 移除做多候选交易对
     */
    public void removeLongCandidate(String instId) {
        if (candidateInstIds.remove(instId)) {
            log.debug("移除做多候选交易对: {}", instId);
        }
    }

    /**
     * 添加做空候选交易对
     */
    public void addShortCandidate(String instId) {
        if (shortCandidateInstIds.add(instId)) {
            log.debug("添加做空候选交易对: {}", instId);
        }
    }

    /**
     * 移除做空候选交易对
     */
    public void removeShortCandidate(String instId) {
        if (shortCandidateInstIds.remove(instId)) {
            log.debug("移除做空候选交易对: {}", instId);
        }
    }

    /**
     * 处理做多候选交易对价格变化
     */
    private void handleLongCandidatePriceChange(String instId, double oldPrice, double newPrice,
                                                double changePercent, KLineEntity klineData) {
        // 如果价格上涨超过2%，可能需要重新评估入场时机
        if (changePercent > 2.0) {
            log.info("做多候选交易对 {} 价格上涨 {:.2f}%，当前价格: {}",
                instId, changePercent, newPrice);
            // 这里可以触发重新评估逻辑
        }

        // 如果价格下跌超过5%，可能是更好的入场机会
        if (changePercent < -5.0) {
            log.info("做多候选交易对 {} 价格下跌 {:.2f}%，当前价格: {}，可能是更好的入场机会",
                instId, Math.abs(changePercent), newPrice);
            // 这里可以触发紧急入场评估
        }
    }

    /**
     * 处理做空候选交易对价格变化
     */
    private void handleShortCandidatePriceChange(String instId, double oldPrice, double newPrice,
                                                 double changePercent, KLineEntity klineData) {
        // 如果价格下跌超过2%，可能需要重新评估入场时机
        if (changePercent < -2.0) {
            log.info("做空候选交易对 {} 价格下跌 {:.2f}%，当前价格: {}",
                instId, Math.abs(changePercent), newPrice);
            // 这里可以触发重新评估逻辑
        }

        // 如果价格上涨超过5%，可能是更好的入场机会
        if (changePercent > 5.0) {
            log.info("做空候选交易对 {} 价格上涨 {:.2f}%，当前价格: {}，可能是更好的入场机会",
                instId, changePercent, newPrice);
            // 这里可以触发紧急入场评估
        }
    }

    /**
     * 获取做多候选交易对数量
     */
    public int getLongCandidateCount() {
        return candidateInstIds.size();
    }

    /**
     * 获取做空候选交易对数量
     */
    public int getShortCandidateCount() {
        return shortCandidateInstIds.size();
    }

    /**
     * 获取所有候选交易对
     */
    public Set<String> getAllCandidates() {
        Set<String> allCandidates = ConcurrentHashMap.newKeySet();
        allCandidates.addAll(candidateInstIds);
        allCandidates.addAll(shortCandidateInstIds);
        return allCandidates;
    }

    /**
     * 清理所有候选交易对
     */
    public void clearAllCandidates() {
        candidateInstIds.clear();
        shortCandidateInstIds.clear();
        log.info("已清理所有候选交易对");
    }

    /**
     * 检查是否为做多候选
     */
    public boolean isLongCandidate(String instId) {
        return candidateInstIds.contains(instId);
    }

    /**
     * 检查是否为做空候选
     */
    public boolean isShortCandidate(String instId) {
        return shortCandidateInstIds.contains(instId);
    }
}
