package com.miner.strategy;

import com.alibaba.fastjson.JSONObject;
import com.miner.strategy.calculator.TechnicalIndicatorCalculator;
import com.miner.strategy.service.SignalEvaluationService;
import com.miner.strategy.service.TradeExecutionService;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.okx.bean.Position;
import com.miner.system.okx.bean.account.param.SetLeverage;
import com.miner.system.okx.service.account.AccountAPIService;
import com.miner.system.okx.service.account.impl.AccountAPIServiceImpl;
import jodd.util.ThreadUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EMA多头排列回调策略
 * 基于指数移动平均线(EMA)的多头排列形态，在价格回调至指定区域时，通过小周期确认后入场做多
 * <p>
 * 策略执行流程：
 * 1. 筛选出符合EMA多头排列的交易对
 * 2. 检测价格是否在回调区域(EMA21与EMA55之间的指定比例)
 * 3. 将符合以上条件的交易对标记为候选者并持续监控
 * 4. 通过小周期强度评分系统检测反转信号，评分超过阈值时入场
 * 5. 设置ATR倍数的止盈止损
 * <p>
 * 核心理念：趋势跟随，回调入场，小周期确认，严格风控
 */
@Component
@Slf4j
public class EMARetraceStrategy extends BaseStrategy {

    // ---------- 服务依赖 ----------

    @Autowired
    private TechnicalIndicatorCalculator indicatorCalculator;

    @Autowired
    private SignalEvaluationService signalEvaluationService;

    @Autowired
    private TradeExecutionService tradeExecutionService;

    // ---------- 策略参数 ----------

    // EMA参数
    private static final int EMA_FAST = 9;
    private static final int EMA_MID = 21;
    private static final int EMA_SLOW = 55;

    // 主周期: 4小时K线
    private static final String MAIN_TIMEFRAME = "4H";
    // 次周期: 5分钟K线
    private static final String SUB_TIMEFRAME = "5m";

    // 回调深度比例（可配置，范围60%-90%）
    private static final double RETRACE_DEPTH = 0.8;

    // ATR参数
    private static final int ATR_PERIOD = 14;
    private static final double STOP_LOSS_ATR_MULTIPLIER = 2;
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_1 = 10.0; // 10倍ATR平仓50%
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_2 = 25.0; // 25倍ATR平掉全部仓位

    // RSI参数 (用于小周期确认)
    private static final int RSI_PERIOD = 14;
    private static final double RSI_OVERSOLD = 30.0;
    private static final double RSI_RISING = 40.0;
    // 做空RSI参数
    private static final double RSI_OVERBOUGHT = 70.0;
    private static final double RSI_FALLING = 60.0;


    private static final String STRATEGY_KEY_PREFIX = "EMARetraceStrategy:";
    // 符合条件交易对的缓存前缀
    private static final String CANDIDATE_KEY_PREFIX = "EMARetraceCandidate:";
    // 候选者过期时间（小时）
    private static final int CANDIDATE_EXPIRE_HOURS = 24;

    // 做空策略参数
    private static final String SHORT_STRATEGY_KEY_PREFIX = "EMARetraceShortStrategy:";
    private static final String SHORT_CANDIDATE_KEY_PREFIX = "EMARetraceShortCandidate:";
    private static final double SHORT_RETRACE_DEPTH = 0.8; // 做空回调深度
    private static final double EMA_SLOPE_THRESHOLD = -0.15; // EMA55下降斜率阈值
    // 做空止损ATR倍数（比做多更保守）
    private static final double SHORT_STOP_LOSS_ATR_MULTIPLIER = 1.5;
    // 做空强信号阈值（适当降低以增加入场机会）
    private static final double SHORT_SIGNAL_THRESHOLD = 0.5;

    // ADX参数
    private static final int ADX_PERIOD = 14;
    private static final double ADX_STRONG_TREND_THRESHOLD = 25.0; // ADX强趋势阈值

    // 信号强度阈值常量
    private static final double STRONG_MACD_THRESHOLD = 0.55;
    private static final double STRONG_BOLLINGER_THRESHOLD = 0.55;
    private static final double STRONG_RSI_THRESHOLD = 0.55;
    private static final double STRONG_PRICE_BREAKOUT_THRESHOLD = 0.6;
    private static final double STRONG_PATTERN_THRESHOLD = 0.65;
    private static final double STRONG_OBV_THRESHOLD = 0.8;

    // 组合信号阈值常量
    private static final double COMBINED_SIGNAL_THRESHOLD = 0.45;
    private static final double COMBINED_VOLUME_THRESHOLD = 0.5;

    // 量价分析常量
    private static final int VOLUME_ANALYSIS_MIN_BARS = 20;
    private static final int OBV_TREND_BARS = 10;
    private static final double VOLUME_BREAKOUT_THRESHOLD = 2.0;
    private static final double VOLUME_INCREASE_THRESHOLD = 1.3;
    private static final double VOLUME_SHRINK_THRESHOLD = 0.7;

    @Override
    public void run() {
        try {
            // 先判断BTC-USDT-SWAP的4小时EMA55斜率是否向上
            String btcInstId = "BTC-USDT-SWAP";
            List<KLineEntity> btcKline4h = getKline(btcInstId, MAIN_TIMEFRAME, "100");
            if (btcKline4h.isEmpty()) {
                log.info("获取BTC-USDT-SWAP K线数据失败，策略终止");
                return;
            }

            double btcEmaSlope = calculateEmaSlope(btcKline4h);
            boolean btcUptrend = btcEmaSlope > 0.15;
            boolean btcDowntrend = btcEmaSlope < -0.15;

            if (!btcUptrend && !btcDowntrend) {
                log.info("BTC-USDT-SWAP的4小时EMA55斜率处于中性区间，策略终止");
                return;
            }

            // 获取交易对列表
            List<String> instIds = getInstIds(40);
            if (instIds.isEmpty()) {
                return;
            }

            // 处理每个交易对
            for (String instId : instIds) {
                // 如果BTC趋势向上，执行做多策略
                if (btcUptrend) {
                    // 先检查做多候选交易对
                    checkCandidateEntry(instId);
                    // 再识别新的做多候选交易对
                    findNewCandidates(instId);
                }

                // 如果BTC趋势向下，执行做空策略
                if (btcDowntrend) {
                    // 先检查做空候选交易对
                    checkShortCandidateEntry(instId);
                    // 再识别新的做空候选交易对
                    findShortCandidates(instId);
                }
            }
        } catch (Exception e) {
            log.error("EMA策略运行异常", e);
        }
    }

    /**
     * 寻找新的符合条件的候选交易对
     */
    private void findNewCandidates(String instId) {
        try {
            // 检查是否已有仓位
            if (checkTrade(STRATEGY_KEY_PREFIX + instId)) {
                return;
            }

            // 检查是否已经是候选交易对
            if (isCandidateInst(instId)) {
                return;
            }

            // 获取主周期K线数据
            List<KLineEntity> kline4h = getKline(instId, MAIN_TIMEFRAME, "100");
            if (kline4h.isEmpty()) {
                return;
            }

            // 计算当前价格
            BigDecimal currentPrice = kline4h.get(0).getClose();
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 提取收盘价
            List<Double> closePrices = kline4h.stream()
                .map(k -> k.getClose().doubleValue())
                .collect(java.util.stream.Collectors.toList());

            // 计算EMA指标 - 使用缓存的技术指标计算器
            double emaFast = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, kline4h, EMA_FAST);
            double emaMid = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, kline4h, EMA_MID);
            double emaSlow = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, kline4h, EMA_SLOW);

            // 检查EMA55斜率
            double emaSlowSlope = calculateEmaSlope(kline4h);

            // 策略1: 原有的EMA多头排列回调策略
            if (checkEmaBullishAlignment(kline4h) && isPriceInRetraceZone(kline4h, kline4h.get(0).getLow())) {
                // 记录候选交易对
//                log.info("发现符合回调区间的交易对: {}, 当前价格: {}", instId, currentPrice);
                saveCandidateInst(instId);

                // 发送提醒消息
//                messageService.send(instId + "回调区间提醒",
//                    MessageFormat.format("{0} 已进入回调区间，当前价格: {1}", instId, currentPrice));
            }
            // 策略2: EMA55趋势向上，检查价格是否在EMA55下方的动态支撑位
            // 最小斜率阈值：0.1%，意味着每5根K线EMA55至少上涨0.1%才认为是有效的上升趋势
            else if (emaSlowSlope > 0.1) {
                // 检查动态支撑位
                SupportZoneResult supportZone = checkDynamicEma55SupportZone(currentPrice.doubleValue(), emaSlow, kline4h);

                if (supportZone.isInSupportZone()) {
                    // 记录候选交易对
//                    log.info("发现EMA55上升趋势(斜率={}%)且价格在动态支撑位的交易对: {}, 当前价格: {}, EMA55: {}, 波动率: {}%, 支撑区间: {}%~{}%",
//                        String.format("%.2f", emaSlowSlope),
//                        instId, currentPrice, String.format("%.4f", emaSlow),
//                        String.format("%.2f", supportZone.getAtrPercent()),
//                        String.format("%.2f", supportZone.getMinDistancePercent()),
//                        String.format("%.2f", supportZone.getMaxDistancePercent()));
                    saveCandidateInst(instId);
                    // 发送提醒消息
//                    messageService.send(instId + "EMA55动态支撑位提醒",
//                        MessageFormat.format("{0} 已接近EMA55动态支撑位，斜率={1}%，当前价格: {2}, EMA55: {3}, 波动率: {4}%",
//                            instId, String.format("%.2f", emaSlowSlope), currentPrice,
//                            String.format("%.4f", emaSlow), String.format("%.2f", supportZone.getAtrPercent())));
                }
            }
        } catch (Exception e) {
            log.error("处理交易对候选 {} 发生异常", instId, e);
        }
    }

    /**
     * 计算动态EMA55支撑区域
     * 该方法基于市场当前波动率动态计算价格与EMA55之间的合理支撑区域
     * <p>
     * 核心原理：
     * 1. 使用ATR指标测量市场波动性，波动性越大，支撑区间范围越宽
     * 2. 将ATR标准化为价格的百分比，以适应不同价格量级的交易对
     * 3. 动态计算支撑区间的上下边界，使其随波动率自适应调整
     * 4. 判断当前价格是否落在此动态区间内
     *
     * @param price     当前价格
     * @param ema55     EMA55均线值
     * @param klineList K线数据列表
     * @return 支撑区域结果对象，包含计算的各项参数和判断结果
     */
    private SupportZoneResult checkDynamicEma55SupportZone(double price, double ema55, List<KLineEntity> klineList) {
        // 步骤1: 计算ATR作为波动率指标 - 使用缓存的技术指标计算器
        // ATR(平均真实波幅)能够衡量市场波动性，反映价格的平均变动幅度
        String instId = klineList.get(0).getInstId();
        double atr = indicatorCalculator.calculateATR(instId, MAIN_TIMEFRAME, klineList, ATR_PERIOD);

        // 步骤2: 将ATR转换为价格的百分比表示
        // 这样可以标准化波动率，使不同价格水平的交易对可比较
        // 例如：如果atrPercent=2，表示价格平均每天波动约2%
        double atrPercent = (atr / price) * 100;

        // 步骤3: 基于波动率动态调整支撑位区间的上下边界
        // 3.1: 计算最小距离，基础值0.5%加上波动率贡献
        // 波动率越大，最小距离越大，防止在高波动市场中过早入场
        double minDistancePercent = 0.5 + (atrPercent * 0.5);

        // 3.2: 计算最大距离，基础值1.5%加上波动率贡献，但设置上限为5%
        // 波动率越大，最大距离越大，但有上限防止距离过大导致错过机会
        double maxDistancePercent = Math.min(1.5 + (atrPercent * 0.8), 5.0);

        // 步骤4: 计算当前价格与EMA55的实际距离(百分比)
        // 正值表示价格低于EMA55，负值表示价格高于EMA55
        double distancePercent = (ema55 - price) / ema55 * 100;

        // 步骤5: 判断价格是否在动态支撑区域内
        // 价格需要低于EMA55(正距离)，且距离在最小和最大范围之间
        boolean inSupportZone = distancePercent > minDistancePercent && distancePercent < maxDistancePercent;

        // 返回包含所有计算结果的对象，便于上层逻辑使用和记录日志
        return new SupportZoneResult(inSupportZone, atrPercent, minDistancePercent, maxDistancePercent, distancePercent);
    }

    /**
     * 检查价格是否在回调区域
     * 回调区域：EMA21与EMA55之间距离的80%处
     */
    private boolean isPriceInRetraceZone(List<KLineEntity> klineList, BigDecimal low) {
        if (klineList.size() < EMA_SLOW + 1) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算EMA指标 - 使用缓存的技术指标计算器
        double emaMid = indicatorCalculator.calculateEMA(klineList.get(0).getInstId(), MAIN_TIMEFRAME, klineList, EMA_MID);
        double emaSlow = indicatorCalculator.calculateEMA(klineList.get(0).getInstId(), MAIN_TIMEFRAME, klineList, EMA_SLOW);

        // 计算回调目标区域 - 修复：确保多头排列时的正确计算
        double distance = Math.abs(emaMid - emaSlow);
        double targetPrice;

        // 确保是多头排列（emaMid > emaSlow）
        if (emaMid > emaSlow) {
            targetPrice = emaMid - distance * RETRACE_DEPTH;
        } else {
            // 如果不是多头排列，返回false
            return false;
        }

//        log.info("[{}]回调区域检查: EMA21={}, EMA55={}, 距离={}, 目标价格={}, 当前价格={}, 是否在回调区域={}",
//            klineList.get(0).getInstId(),
//            String.format(String.format("%%.%df", low.scale()), emaMid),
//            String.format(String.format("%%.%df", low.scale()), emaSlow),
//            String.format(String.format("%%.%df", low.scale()), distance),
//            String.format(String.format("%%.%df", low.scale()), targetPrice),
//            String.format(String.format("%%.%df", low.scale()), klineList.get(0).getClose()),
//            low.doubleValue() < targetPrice ? "是" : "否"
//        );

        return low.doubleValue() <= targetPrice;
    }

    /**
     * 检查候选交易对是否满足入场条件
     * 策略逻辑：一旦交易对进入回调区间，就持续监控直到形成入场信号或EMA多头排列不再满足
     */
    private void checkCandidateEntry(String instId) {
        try {
            // 检查是否为候选交易对
            if (!isCandidateInst(instId)) {
                return;
            }

            // 检查是否已有仓位
            if (checkTrade(STRATEGY_KEY_PREFIX + instId) || getPos(instId) != null) {
                // 已有仓位，移除候选状态
                removeCandidateInst(instId);
                return;
            }

            // 获取K线数据
            List<KLineEntity> kline4h = getKline(instId, MAIN_TIMEFRAME, "100");
            List<KLineEntity> kline5m = getKline(instId, SUB_TIMEFRAME, "100");

            if (kline4h.isEmpty() || kline5m.isEmpty()) {
                return;
            }

            // 计算当前价格
            BigDecimal currentPrice = kline5m.get(0).getClose();
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 再次确认EMA多头排列条件
            if (calculateEmaSlope(kline4h) < 0.1) {
                // 不再满足EMA多头排列条件，移除候选状态
                removeCandidateInst(instId);
                return;
            }

            // 通过小周期确认入场信号 - 使用重构后的信号评估服务
            SignalEvaluationService.SignalEvaluationResult result = signalEvaluationService.evaluateLongSignal(kline5m);

            // 计算动态阈值
            double baseThreshold = 3.8;
            double dynamicThreshold = calculateDynamicThreshold(kline5m, baseThreshold);

            // 判断是否满足入场条件
            boolean entrySignal = result.getScoreRatio() >= dynamicThreshold;

            // 强信号判定（如果有强信号组合，可以降低总体阈值）
            if (result.hasStrongSignal() && result.getScoreRatio() >= dynamicThreshold * 0.85) {
                entrySignal = true;
                log.info("{} 强信号组合触发，降低入场阈值: {}", instId, String.format("%.1f", dynamicThreshold * 0.85));
            }

            if (entrySignal) {
                log.info("候选交易对 {} 满足小周期确认条件，准备入场", instId);

                // 执行入场操作 - 使用重构后的交易执行服务
                boolean success = tradeExecutionService.executeLongEntry(instId, currentPrice, kline4h);

                if (success) {
                    // 入场成功后移除候选状态
                    removeCandidateInst(instId);
                }
            }
        } catch (Exception e) {
            log.error("检查候选交易对 {} 入场条件时发生异常", instId, e);
        }
    }

    /**
     * 保存候选交易对到Redis
     */
    private void saveCandidateInst(String instId) {
        String key = CANDIDATE_KEY_PREFIX + instId;
        // 修复：使用常量定义的过期时间
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, System.currentTimeMillis(), Duration.ofHours(CANDIDATE_EXPIRE_HOURS));
    }

    /**
     * 检查是否为候选交易对
     */
    private boolean isCandidateInst(String instId) {
        String key = CANDIDATE_KEY_PREFIX + instId;
        return com.miner.common.utils.redis.RedisUtils.getCacheObject(key) != null;
    }

    /**
     * 移除候选交易对
     */
    private void removeCandidateInst(String instId) {
        String key = CANDIDATE_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.deleteObject(key);
    }

    /**
     * 检查是否满足EMA多头排列条件 (EMA9 > EMA21 > EMA55)
     */
    private boolean checkEmaBullishAlignment(List<KLineEntity> klineList) {
        if (klineList.size() < EMA_SLOW + 1) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算EMA指标 - 使用缓存的技术指标计算器
        String instId = klineList.get(0).getInstId();
        double emaFast = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, klineList, EMA_FAST);
        double emaMid = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, klineList, EMA_MID);
        double emaSlow = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, klineList, EMA_SLOW);

        // 检查多头排列条件：EMA9 > EMA21 > EMA55

        // 检查EMA斜率是否向上 (可选增强条件)
//        boolean emaSlope = calculateEmaSlope(klineList) > 0;

//        log.info("EMA多头排列检查: EMA9={}, EMA21={}, EMA55={}, 结果={}",
//            String.format("%.4f", emaFast),
//            String.format("%.4f", emaMid),
//            String.format("%.4f", emaSlow),
////            emaSlope ? "向上" : "向下",
//            isAligned ? "满足" : "不满足");

        return emaFast > emaMid && emaMid > emaSlow;
    }

    /**
     * 计算EMA斜率（判断趋势强度）
     * 使用百分比变化衡量斜率，可以更好地应用于不同价格水平的交易对
     *
     * @param klineList K线数据列表
     * @return EMA55的斜率百分比，正值表示上升趋势，数值越大表示趋势越强
     */
    private double calculateEmaSlope(List<KLineEntity> klineList) {
        if (klineList.size() < EMA_SLOW + 10) {
            return 0;
        }

        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算当前、5根K线前和10根K线前的EMA55，以检测斜率的加速度
        String instId = klineList.get(0).getInstId();
        double currentEma = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, klineList, EMA_SLOW);

        // 为了计算历史EMA，我们需要使用子列表
        List<KLineEntity> kline5Ago = klineList.subList(5, klineList.size());
        List<KLineEntity> kline10Ago = klineList.subList(10, klineList.size());

        double previousEma5 = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, kline5Ago, EMA_SLOW);
        double previousEma10 = indicatorCalculator.calculateEMA(instId, MAIN_TIMEFRAME, kline10Ago, EMA_SLOW);

        // 计算5根K线的变化率
        double slope5 = (currentEma - previousEma5) / previousEma5 * 100;

        // 计算10根K线的变化率，用于判断长期趋势
        double slope10 = (currentEma - previousEma10) / previousEma10 * 100;

        // 通过5K和10K的斜率，判断趋势是否加速
        boolean isAccelerating = slope5 > slope10 / 2; // 短期斜率应该比长期斜率的一半还大

        // 返回短期斜率，并在加速时给予额外加分
        return isAccelerating ? slope5 * 1.2 : slope5;
    }

    /**
     * 小周期入场信号确认（多因子量价分析增强版）
     * <p>
     * 主要功能：
     * 1. 检查K线形态、RSI、MACD、布林带、价格突破等常规信号
     * 2. 增强量价关系分析：
     * - 成交量形态（量能突破、量能放大、量能萎缩）
     * - OBV指标（趋势、突破、背离）
     * - 量价背离（如价格创新低但OBV未创新低）
     * 3. 所有信号纳入加权评分系统，动态阈值判断是否入场
     * 4. 日志详细输出各项信号强度及量价分析细节，便于调试和回测
     *
     * @param kline5m 5分钟K线数据，最新K线在第0位
     * @return true 满足入场条件，false 不满足
     */
    private boolean confirmEntrySignal(List<KLineEntity> kline5m) {
        // 快速验证数据有效性
        if (kline5m == null || kline5m.size() < VOLUME_ANALYSIS_MIN_BARS) {
            log.debug("K线数据不足，无法进行信号确认分析");
            return false;
        }

        // 1. 计算常规信号强度（0-1范围）
        double bullishPatternStrength = checkBullishCandlePatternStrength(kline5m); // 看涨K线形态
        double rsiStrength = checkRsiStrength(kline5m); // RSI超卖回升
        double volumeStrength = checkVolumeStrength(kline5m); // 成交量放大
        double macdStrength = checkMacdStrength(kline5m); // MACD金叉/背离
        double priceBreakoutStrength = checkPriceBreakoutStrength(kline5m); // 价格突破
        double bollingerStrength = checkBollingerBandStrength(kline5m); // 布林带信号

        // 2. 量价关系分析结果
        VolumePriceAnalysisResult vpResult = analyzeVolumePrice(kline5m);

        // 3. 日志输出所有信号强度及量价分析
        String instId = kline5m.get(0).getInstId();
        log.info("{} 小周期反转确认: 看涨K线形态={}, RSI超卖回升={}, 成交量放大={}, MACD信号={}, 价格突破={}, 布林带信号={}, 量价分析:{}",
            instId,
            String.format("%.2f", bullishPatternStrength),
            String.format("%.2f", rsiStrength),
            String.format("%.2f", volumeStrength),
            String.format("%.2f", macdStrength),
            String.format("%.2f", priceBreakoutStrength),
            String.format("%.2f", bollingerStrength),
            vpResult.getLogInfo()
        );

        // 4. 计算加权评分
        SignalScoreResult scoreResult = calculateSignalScore(
            bullishPatternStrength, rsiStrength, volumeStrength, macdStrength,
            priceBreakoutStrength, bollingerStrength, vpResult);

        // 5. 动态阈值（根据市场状态自适应）
        double baseThreshold = 3.8;
        double dynamicThreshold = calculateDynamicThreshold(kline5m, baseThreshold);

        // 6. 记录评分详情（所有信号分数及权重）
        log.info("{} 反转强度评分: {}/{} = {}/10, 动态阈值={} {}",
            instId,
            String.format("%.1f", scoreResult.getTotalScore()),
            String.format("%.1f", scoreResult.getMaxPossibleScore()),
            String.format("%.1f", scoreResult.getScoreRatio()),
            String.format("%.1f", dynamicThreshold),
            scoreResult.getDetailedScoreLog()
        );

        // 7. 强信号和组合信号判定（满足其一即可入场）
        // 增强版强信号判定，分类并要求多条件满足
        boolean hasStrongSignal = false;

        // 1. 计算各类指标超过阈值的数量
        int strongTechnicalCount = 0;  // 技术指标：MACD, RSI, 布林带
        strongTechnicalCount += (macdStrength > STRONG_MACD_THRESHOLD) ? 1 : 0;
        strongTechnicalCount += (rsiStrength > STRONG_RSI_THRESHOLD) ? 1 : 0;
        strongTechnicalCount += (bollingerStrength > STRONG_BOLLINGER_THRESHOLD) ? 1 : 0;

        int strongPriceCount = 0;  // 价格形态：K线形态，价格突破
        strongPriceCount += (bullishPatternStrength > STRONG_PATTERN_THRESHOLD) ? 1 : 0;
        strongPriceCount += (priceBreakoutStrength > STRONG_PRICE_BREAKOUT_THRESHOLD) ? 1 : 0;

        int strongVolumeCount = 0;  // 量价指标：OBV背离，OBV突破，成交量
        strongVolumeCount += (vpResult.getObvDivergenceStrength() > STRONG_OBV_THRESHOLD) ? 1 : 0;
        strongVolumeCount += (vpResult.getObvBreakoutStrength() > STRONG_OBV_THRESHOLD) ? 1 : 0;
        strongVolumeCount += (volumeStrength > STRONG_PATTERN_THRESHOLD) ? 1 : 0;

        // 2. 强信号判定条件组合（必须满足下列条件之一）
        // 条件A: 至少3个指标超过阈值，且这些指标来自至少2个不同的分类
        boolean conditionA = (strongTechnicalCount + strongPriceCount + strongVolumeCount) >= 3 &&
            ((strongTechnicalCount > 0 ? 1 : 0) +
                (strongPriceCount > 0 ? 1 : 0) +
                (strongVolumeCount > 0 ? 1 : 0)) >= 2;

        // 条件B: 技术指标超强: 所有主要技术指标都很强
        boolean conditionB = macdStrength > 0.7 && rsiStrength > 0.7 && bollingerStrength > 0.6;

        // 条件C: 量价配合特别好: 价格突破与量能突破共同确认
        boolean conditionC = priceBreakoutStrength > 0.65 &&
            (vpResult.getVolumeBreakoutStrength() > 0.7 || volumeStrength > 0.7);

        // 条件D: 强烈的底部反转: RSI超卖后显著回升 + 明显的K线反转形态 + 至少一项辅助指标
        boolean conditionD = rsiStrength > 0.6 && bullishPatternStrength > 0.7 &&
            (macdStrength > 0.5 || bollingerStrength > 0.5 ||
                vpResult.getObvDivergenceStrength() > 0.6);

        // 条件E: 量价背离明确，且有辅助指标支持
        boolean conditionE = vpResult.getObvDivergenceStrength() > 0.8 &&
            (macdStrength > 0.5 || rsiStrength > 0.5 || bullishPatternStrength > 0.6);

        // 任一条件满足即认为有强信号
        hasStrongSignal = conditionA || conditionB || conditionC || conditionD || conditionE;

        // 组合信号判定（改进版）
        // 1. 计算各类组合信号
        Map<String, Double> combinedScores = new HashMap<>();

        // 计算技术指标组合得分
        combinedScores.put("技术指标组合", calculateTechnicalIndicatorScore(
            macdStrength, rsiStrength, bollingerStrength));

        // 计算价格形态组合得分
        combinedScores.put("价格形态组合", calculatePricePatternScore(
            bullishPatternStrength, priceBreakoutStrength));

        // 计算量价关系组合得分
        combinedScores.put("量价关系组合", calculateVolumePriceScore(
            volumeStrength, vpResult));

        // 计算跨类型组合得分
        combinedScores.put("趋势确认组合", calculateTrendConfirmationScore(
            macdStrength, priceBreakoutStrength, vpResult.getVolumeBreakoutStrength()));

        combinedScores.put("底部反转组合", calculateBottomReversalScore(
            rsiStrength, bullishPatternStrength, vpResult.getObvDivergenceStrength()));

        // 2. 记录组合信号得分
        String combinedSignalLog = formatCombinedScoreLog(combinedScores);
        log.debug("{} 组合信号得分: {}", kline5m.get(0).getInstId(), combinedSignalLog);

        // 3. 根据市场环境确定组合信号阈值
        double combinedScoreThreshold = getCombinedScoreThreshold(dynamicThreshold);

        // 4. 判断是否有满足阈值的组合信号
        boolean hasCombinedSignal = hasAnyCombinedSignalAboveThreshold(combinedScores, combinedScoreThreshold);

        // 5. 根据信号类型调整最终阈值
        double finalScoreThreshold = adjustFinalThreshold(dynamicThreshold, hasStrongSignal, hasCombinedSignal);

        // 6. 确定最终入场信号
        boolean entrySignal = false;

        // 情况1: 评分特别高 (超过阈值的1.5倍)，信号要求可以适当放宽
        if (scoreResult.getScoreRatio() >= finalScoreThreshold * 1.5) {
            entrySignal = true;
            log.info("{} 总评分极高，直接触发入场信号: {}/10", kline5m.get(0).getInstId(), String.format("%.1f", scoreResult.getScoreRatio()));
        }
        // 情况2: 评分高且有强信号
        else if (scoreResult.getScoreRatio() >= finalScoreThreshold && hasStrongSignal) {
            entrySignal = true;
        }
        // 情况3: 评分高且有组合信号，但要求评分更高一些
        else if (scoreResult.getScoreRatio() >= finalScoreThreshold * 1.1 && hasCombinedSignal) {
            entrySignal = true;
        }
        // 情况4: 评分达标且同时存在强信号和组合信号，门槛可以适当降低
        else if (scoreResult.getScoreRatio() >= finalScoreThreshold * 0.9 && hasStrongSignal && hasCombinedSignal) {
            entrySignal = true;
        }

        // 7. 记录详细决策过程
        logEntrySignalDecision(kline5m.get(0).getInstId(), scoreResult.getScoreRatio(),
            finalScoreThreshold, hasStrongSignal, hasCombinedSignal, entrySignal);

        return entrySignal;
    }

    /**
     * 分析量价关系，抽取为独立方法以提高代码可读性
     *
     * @param kline5m K线数据
     * @return 量价分析结果
     */
    private VolumePriceAnalysisResult analyzeVolumePrice(List<KLineEntity> kline5m) {
        double volumeBreakoutStrength = 0.0;
        double volumeShrinkStrength = 0.0;
        double obvTrendStrength = 0.0;
        double obvBreakoutStrength = 0.0;
        double obvDivergenceStrength = 0.0;
        StringBuilder volumeLog = new StringBuilder();

        if (kline5m.size() < VOLUME_ANALYSIS_MIN_BARS) {
            return new VolumePriceAnalysisResult(
                volumeBreakoutStrength, volumeShrinkStrength, obvTrendStrength,
                obvBreakoutStrength, obvDivergenceStrength, volumeLog.toString()
            );
        }

        // 2.1 成交量形态识别
        double currVolume = kline5m.get(0).getVolume();
        double avgVolume10 = calculateAvgVolume(kline5m, 1, 11);
        double avgVolume20 = calculateAvgVolume(kline5m, 1, 21);

        // 量能突破
        if (currVolume > avgVolume10 * VOLUME_BREAKOUT_THRESHOLD && currVolume > avgVolume20 * 1.5) {
            volumeBreakoutStrength = 1.0;
            volumeLog.append("量能突破 ");
        } else if (currVolume > avgVolume10 * VOLUME_INCREASE_THRESHOLD) {
            volumeBreakoutStrength = 0.6;
            volumeLog.append("量能放大 ");
        }
        // 量能萎缩
        if (currVolume < avgVolume10 * VOLUME_SHRINK_THRESHOLD && currVolume < avgVolume20 * VOLUME_SHRINK_THRESHOLD) {
            volumeShrinkStrength = 0.7;
            volumeLog.append("量能萎缩 ");
        }

        // 2.2 OBV指标分析
        List<Double> closeList = extractClosePrices(kline5m);
        List<Double> volumeList = extractVolumes(kline5m);
        List<Double> obvList = calculateOBV(closeList, volumeList);

        if (obvList.size() >= OBV_TREND_BARS) {
            // OBV趋势（近10根单调性）
            int obvTrend = isMonotonic(obvList.subList(0, OBV_TREND_BARS));
            if (obvTrend == 1) {
                obvTrendStrength = 0.8;
                volumeLog.append("OBV上升 ");
            } else if (obvTrend == -1) {
                obvTrendStrength = 0.0;
                volumeLog.append("OBV下降 ");
            } else {
                obvTrendStrength = 0.3;
            }

            // OBV突破（新高/新低）
            double obvNow = obvList.get(0);
            double obvMax10 = getMaxValue(obvList.subList(1, 11));
            double obvMin10 = getMinValue(obvList.subList(1, 11));

            if (obvNow > obvMax10) {
                obvBreakoutStrength = 1.0;
                volumeLog.append("OBV新高 ");
            } else if (obvNow < obvMin10) {
                obvBreakoutStrength = 0.0;
                volumeLog.append("OBV新低 ");
            } else {
                obvBreakoutStrength = 0.4;
            }

            // OBV背离（价格创新低但OBV未创新低）
            double priceMin10 = getMinValue(closeList.subList(1, 11));
            if (closeList.get(0) < priceMin10 && obvNow > obvMin10) {
                obvDivergenceStrength = 1.0;
                volumeLog.append("量价背离 ");
            }
        }

        return new VolumePriceAnalysisResult(
            volumeBreakoutStrength, volumeShrinkStrength, obvTrendStrength,
            obvBreakoutStrength, obvDivergenceStrength, volumeLog.toString()
        );
    }

    /**
     * 计算加权评分，抽取为独立方法
     */
    private SignalScoreResult calculateSignalScore(
        double bullishPatternStrength, double rsiStrength, double volumeStrength, double macdStrength,
        double priceBreakoutStrength, double bollingerStrength, VolumePriceAnalysisResult vpResult) {

        // 各信号权重
        double macdWeight = 2.0;
        double bollingerWeight = 2.0;
        double rsiWeight = 2.0;
        double priceBreakoutWeight = 2.0;
        double candlePatternWeight = 1.5;
        double volumeWeight = 1.5;
        double volumeBreakoutWeight = 1.0;
        double volumeShrinkWeight = 0.7;
        double obvTrendWeight = 1.0;
        double obvBreakoutWeight = 1.0;
        double obvDivergenceWeight = 1.2;

        // 加权分数累加
        double totalScore = 0.0;
        totalScore += macdStrength * macdWeight;
        totalScore += bollingerStrength * bollingerWeight;
        totalScore += rsiStrength * rsiWeight;
        totalScore += priceBreakoutStrength * priceBreakoutWeight;
        totalScore += bullishPatternStrength * candlePatternWeight;
        totalScore += volumeStrength * volumeWeight;
        totalScore += vpResult.getVolumeBreakoutStrength() * volumeBreakoutWeight;
        totalScore += vpResult.getVolumeShrinkStrength() * volumeShrinkWeight;
        totalScore += vpResult.getObvTrendStrength() * obvTrendWeight;
        totalScore += vpResult.getObvBreakoutStrength() * obvBreakoutWeight;
        totalScore += vpResult.getObvDivergenceStrength() * obvDivergenceWeight;

        // 最大可能分数
        double maxPossibleScore = macdWeight + bollingerWeight + rsiWeight + priceBreakoutWeight + candlePatternWeight +
            volumeWeight + volumeBreakoutWeight + volumeShrinkWeight + obvTrendWeight + obvBreakoutWeight + obvDivergenceWeight;

        // 评分比例（满分10分）
        double scoreRatio = (totalScore / maxPossibleScore) * 10.0;

        // 构建详细日志
        String detailLog = String.format(
            "(MACD=%s×%s, 布林带=%s×%s, RSI=%s×%s, 价格突破=%s×%s, K线形态=%s×%s, 成交量=%s×%s, 量能突破=%s×%s, 量能萎缩=%s×%s, OBV趋势=%s×%s, OBV突破=%s×%s, OBV背离=%s×%s)",
            String.format("%.2f", macdStrength), String.format("%.1f", macdWeight),
            String.format("%.2f", bollingerStrength), String.format("%.1f", bollingerWeight),
            String.format("%.2f", rsiStrength), String.format("%.1f", rsiWeight),
            String.format("%.2f", priceBreakoutStrength), String.format("%.1f", priceBreakoutWeight),
            String.format("%.2f", bullishPatternStrength), String.format("%.1f", candlePatternWeight),
            String.format("%.2f", volumeStrength), String.format("%.1f", volumeWeight),
            String.format("%.2f", vpResult.getVolumeBreakoutStrength()), String.format("%.1f", volumeBreakoutWeight),
            String.format("%.2f", vpResult.getVolumeShrinkStrength()), String.format("%.1f", volumeShrinkWeight),
            String.format("%.2f", vpResult.getObvTrendStrength()), String.format("%.1f", obvTrendWeight),
            String.format("%.2f", vpResult.getObvBreakoutStrength()), String.format("%.1f", obvBreakoutWeight),
            String.format("%.2f", vpResult.getObvDivergenceStrength()), String.format("%.1f", obvDivergenceWeight)
        );

        return new SignalScoreResult(totalScore, maxPossibleScore, scoreRatio, detailLog);
    }

    /**
     * 计算指定区间的平均成交量
     */
    private double calculateAvgVolume(List<KLineEntity> klineList, int start, int end) {
        return klineList.subList(start, Math.min(end, klineList.size()))
            .stream()
            .mapToDouble(KLineEntity::getVolume)
            .average()
            .orElse(0);
    }

    /**
     * 从K线提取收盘价列表
     */
    private List<Double> extractClosePrices(List<KLineEntity> klineList) {
        return klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 从K线提取成交量列表
     */
    private List<Double> extractVolumes(List<KLineEntity> klineList) {
        return klineList.stream()
            .map(item -> Double.valueOf(item.getVolume()))
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取列表最大值
     */
    private double getMaxValue(List<Double> list) {
        return list.stream()
            .mapToDouble(Double::doubleValue)
            .max()
            .orElse(0);
    }

    /**
     * 获取列表最小值
     */
    private double getMinValue(List<Double> list) {
        return list.stream()
            .mapToDouble(Double::doubleValue)
            .min()
            .orElse(0);
    }

    /**
     * 量价分析结果类，封装量价分析的各项指标强度和日志
     */
    @Data
    private static class VolumePriceAnalysisResult {
        private final double volumeBreakoutStrength;
        private final double volumeShrinkStrength;
        private final double obvTrendStrength;
        private final double obvBreakoutStrength;
        private final double obvDivergenceStrength;
        private final String logInfo;

        public VolumePriceAnalysisResult(double volumeBreakoutStrength, double volumeShrinkStrength,
                                         double obvTrendStrength, double obvBreakoutStrength,
                                         double obvDivergenceStrength, String logInfo) {
            this.volumeBreakoutStrength = volumeBreakoutStrength;
            this.volumeShrinkStrength = volumeShrinkStrength;
            this.obvTrendStrength = obvTrendStrength;
            this.obvBreakoutStrength = obvBreakoutStrength;
            this.obvDivergenceStrength = obvDivergenceStrength;
            this.logInfo = logInfo;
        }
    }

    /**
     * 信号评分结果类，封装所有信号的综合评分结果
     */
    @Data
    private static class SignalScoreResult {
        private final double totalScore;
        private final double maxPossibleScore;
        private final double scoreRatio;
        private final String detailedScoreLog;

        public SignalScoreResult(double totalScore, double maxPossibleScore,
                                 double scoreRatio, String detailedScoreLog) {
            this.totalScore = totalScore;
            this.maxPossibleScore = maxPossibleScore;
            this.scoreRatio = scoreRatio;
            this.detailedScoreLog = detailedScoreLog;
        }

    }

    /**
     * 计算动态阈值
     * 基于市场状态调整基础阈值，以适应不同市场环境
     *
     * @param kline         K线数据
     * @param baseThreshold 基础阈值
     * @return 调整后的动态阈值
     */
    private double calculateDynamicThreshold(List<KLineEntity> kline, double baseThreshold) {
        if (kline.size() < 30) {
            return baseThreshold; // 数据不足时使用基础阈值
        }

        // 1. 计算市场波动性
        double volatilityFactor = calculateVolatilityFactor(kline);

        // 2. 计算趋势强度
        double trendStrength = calculateTrendStrength(kline);

        // 3. 计算成交量变化
        double volumeChangeFactor = calculateVolumeChangeFactor(kline);

        // 阈值调整系数
        double adjustment = 0.0;

        // 高波动性市场 - 提高阈值（需要更强的确认信号）
        if (volatilityFactor > 1.5) {
            adjustment += 0.3 * Math.min((volatilityFactor - 1.5), 1.0);
        }

        // 低波动性市场 - 降低阈值（避免错过机会）
        if (volatilityFactor < 0.7) {
            adjustment -= 0.2 * Math.min((0.7 - volatilityFactor) / 0.4, 1.0);
        }

        // 强趋势市场 - 降低阈值（趋势内回调更可靠）
        if (trendStrength > 0.8) {
            adjustment -= 0.3 * Math.min((trendStrength - 0.8) / 0.2, 1.0);
        }

        // 成交量异常大 - 提高阈值（可能是异常波动）
        if (volumeChangeFactor > 3.0) {
            adjustment += 0.2 * Math.min((volumeChangeFactor - 3.0) / 2.0, 1.0);
        }

        // 限制调整范围在±0.8以内
        adjustment = Math.max(Math.min(adjustment, 0.8), -0.8);

        double finalThreshold = baseThreshold + adjustment;

        // 记录调整逻辑
        log.debug("{} 阈值调整: {}→{} (波动={}, 趋势={}, 成交量={})",
            kline.get(0).getInstId(),
            String.format("%.1f", baseThreshold),
            String.format("%.1f", finalThreshold),
            String.format("%.1f", volatilityFactor),
            String.format("%.1f", trendStrength),
            String.format("%.1f", volumeChangeFactor)
        );

        return finalThreshold;
    }

    /**
     * 计算市场波动性因子
     * 基于ATR和价格比率
     */
    private double calculateVolatilityFactor(List<KLineEntity> kline) {
        double atr = calculateATR(kline, 0, 14);
        double currentPrice = kline.get(0).getClose().doubleValue();

        // 计算ATR占当前价格的百分比
        double atrRatio = (atr / currentPrice) * 100;

        // 标准化: 假设2%为正常水平，返回实际值与正常值的比率
        return atrRatio / 2.0;
    }

    /**
     * 计算趋势强度
     * 基于价格与移动平均线的关系和移动平均线的斜率
     */
    private double calculateTrendStrength(List<KLineEntity> kline) {
        if (kline.size() < 50) {
            return 0.5; // 数据不足返回中性值
        }

        // 计算50周期和20周期指数移动平均线
        List<Double> closePrices = kline.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        double ema20 = calculateEMA(closePrices, 0, 20);
        double ema50 = calculateEMA(closePrices, 0, 50);
        double ema20Prev = calculateEMA(closePrices, 10, 20);
        double ema50Prev = calculateEMA(closePrices, 10, 50);

        // 计算当前价格
        double currentPrice = kline.get(0).getClose().doubleValue();

        // 1. 价格与均线关系分数
        double pricePositionScore = 0.0;
        if (currentPrice > ema20 && ema20 > ema50) {
            // 多头排列，满分
            pricePositionScore = 1.0;
        } else if (currentPrice > ema20) {
            // 价格在短期均线上方，部分得分
            pricePositionScore = 0.7;
        } else if (currentPrice > ema50) {
            // 价格在长期均线上方，低得分
            pricePositionScore = 0.4;
        }

        // 2. 均线斜率分数
        double ema20Slope = (ema20 - ema20Prev) / ema20Prev;
        double ema50Slope = (ema50 - ema50Prev) / ema50Prev;

        double slopeScore = 0.5; // 默认中性

        if (ema20Slope > 0 && ema50Slope > 0) {
            // 双均线向上，高分
            slopeScore = 0.5 + Math.min(ema20Slope * 100, 0.5);
        } else if (ema20Slope > 0) {
            // 短期均线向上，中等分数
            slopeScore = 0.5 + Math.min(ema20Slope * 50, 0.3);
        } else if (ema20Slope < 0 && ema50Slope < 0) {
            // 双均线向下，低分
            slopeScore = 0.5 - Math.min(Math.abs(ema20Slope) * 100, 0.4);
        }

        // 综合得分
        return pricePositionScore * 0.6 + slopeScore * 0.4;
    }

    /**
     * 计算成交量变化因子
     * 基于当前成交量与过去平均值的比率
     */
    private double calculateVolumeChangeFactor(List<KLineEntity> kline) {
        if (kline.size() < 20) {
            return 1.0; // 数据不足返回正常值
        }

        // 计算过去20根K线的平均成交量
        double avgVolume = 0.0;
        for (int i = 1; i < 20; i++) {
            avgVolume += kline.get(i).getVolume();
        }
        avgVolume /= 19.0;

        // 当前成交量与平均值的比率
        if (avgVolume == 0) return 1.0;
        return kline.get(0).getVolume() / avgVolume;
    }

    /**
     * 检查K线形态强度 (0-1)
     */
    private double checkBullishCandlePatternStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return 0.0;
        }

        // 检查各种K线形态的强度，返回最强的一个
        double hammerStrength = checkHammerPatternStrength(klineList.get(0));
        double morningStarStrength = checkMorningStarStrength(klineList);
        double dojiStrength = checkDojiPatternStrength(klineList.get(0));
        double engulfingStrength = checkBullishEngulfingStrength(klineList);

        // 返回最强的形态信号强度
        return Math.max(Math.max(hammerStrength, morningStarStrength),
            Math.max(dojiStrength, engulfingStrength));
    }

    /**
     * 检查锤子线形态强度 (0-1)
     */
    private double checkHammerPatternStrength(KLineEntity kline) {
        double body = Math.abs(kline.getClose().doubleValue() - kline.getOpen().doubleValue());
        double lowerShadow = Math.min(kline.getOpen().doubleValue(), kline.getClose().doubleValue()) - kline.getLow().doubleValue();
        double upperShadow = kline.getHigh().doubleValue() - Math.max(kline.getOpen().doubleValue(), kline.getClose().doubleValue());
        double totalRange = kline.getHigh().doubleValue() - kline.getLow().doubleValue();

        if (totalRange == 0 || body == 0) return 0.0;

        // 锤子线特征：下影线长度至少是实体的两倍，上影线很短
        if (lowerShadow > body * 2 && upperShadow < body * 0.5) {
            // 计算形态质量（0-1）
            double shadowBodyRatio = lowerShadow / body;
            double upperShadowRatio = upperShadow / totalRange;

            // 下影线比例得分(最高0.7)
            double shadowScore = Math.min((shadowBodyRatio - 2.0) / 5.0, 0.7);

            // 上影线比例得分(最高0.3)
            double upperShadowScore = 0.3 * (1.0 - Math.min(upperShadowRatio * 5, 1.0));

            // 如果是阳线加分
            double bullishBonus = kline.getClose().doubleValue() > kline.getOpen().doubleValue() ? 0.2 : 0.0;

            return Math.min(shadowScore + upperShadowScore + bullishBonus, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查启明星形态强度 (0-1)
     */
    private double checkMorningStarStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return 0.0;
        }

        KLineEntity first = klineList.get(2);
        KLineEntity second = klineList.get(1);
        KLineEntity third = klineList.get(0);

        // 基本条件检查
        boolean firstIsBearish = first.getClose().doubleValue() < first.getOpen().doubleValue();
        double firstBodySize = Math.abs(first.getOpen().doubleValue() - first.getClose().doubleValue());
        double secondBodySize = Math.abs(second.getOpen().doubleValue() - second.getClose().doubleValue());
        boolean secondIsSmall = secondBodySize < firstBodySize * 0.5;
        boolean thirdIsBullish = third.getClose().doubleValue() > third.getOpen().doubleValue();
        double thirdBodySize = Math.abs(third.getOpen().doubleValue() - third.getClose().doubleValue());

        if (firstIsBearish && secondIsSmall && thirdIsBullish) {
            // 计算第三根K线相对于第一根K线的回升程度(0-1)
            double retracementRatio = 0.0;
            if (firstBodySize > 0) {
                double retracementAmount = third.getClose().doubleValue() - first.getClose().doubleValue();
                retracementRatio = Math.min(retracementAmount / firstBodySize, 1.0);
                // 回升超过第一根K线实体50%加分
                retracementRatio = retracementRatio > 0.5 ? (0.5 + (retracementRatio - 0.5) * 2) : retracementRatio;
            }

            // 第二根K线越小，形态越明显
            double secondK_quality = Math.max(0.0, 1.0 - (secondBodySize / firstBodySize) * 2);

            // 第三根K线实体越大，信号越强
            double thirdK_strength = Math.min(thirdBodySize / firstBodySize, 1.0);

            // 综合评分
            return Math.min((retracementRatio * 0.5) + (secondK_quality * 0.3) + (thirdK_strength * 0.2), 1.0);
        }

        return 0.0;
    }

    /**
     * 检查十字星形态强度 (0-1)
     */
    private double checkDojiPatternStrength(KLineEntity kline) {
        double body = Math.abs(kline.getClose().doubleValue() - kline.getOpen().doubleValue());
        double totalRange = kline.getHigh().doubleValue() - kline.getLow().doubleValue();

        if (totalRange == 0) return 0.0;

        // 十字星特征：实体非常小，上下影线较长
        double bodyRatio = body / totalRange;

        if (bodyRatio < 0.1) {
            // 完美十字星的实体比例应接近0
            double bodyQuality = Math.max(0.0, 1.0 - (bodyRatio * 10));

            // 平衡上下影线更好（中心点位于中间）
            double centerPoint = (kline.getOpen().doubleValue() + kline.getClose().doubleValue()) / 2;
            double balance = 1.0 - Math.abs((centerPoint - kline.getLow().doubleValue()) / totalRange - 0.5) * 2;

            // 综合评分
            return Math.min(bodyQuality * 0.7 + balance * 0.3, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查看涨吞没形态强度 (0-1)
     */
    private double checkBullishEngulfingStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 2) {
            return 0.0;
        }

        KLineEntity previous = klineList.get(1);
        KLineEntity current = klineList.get(0);

        // 基本条件检查
        boolean previousIsBearish = previous.getClose().doubleValue() < previous.getOpen().doubleValue();
        boolean currentIsBullish = current.getClose().doubleValue() > current.getOpen().doubleValue();

        if (previousIsBearish && currentIsBullish) {
            double prevBody = Math.abs(previous.getOpen().doubleValue() - previous.getClose().doubleValue());
            double currBody = Math.abs(current.getOpen().doubleValue() - current.getClose().doubleValue());

            // 检查吞没程度
            boolean isEngulfing = current.getOpen().doubleValue() <= previous.getClose().doubleValue() &&
                current.getClose().doubleValue() >= previous.getOpen().doubleValue();

            if (isEngulfing) {
                // 吞没比例 (当前实体相对前一根的大小)
                double engulfingRatio = Math.min(currBody / prevBody, 2.0) / 2.0;

                // 额外吞没范围（如果不仅吞没实体还吞没了影线）
                double lowerExcess = Math.max(0.0, previous.getLow().doubleValue() - current.getOpen().doubleValue());
                double upperExcess = Math.max(0.0, current.getClose().doubleValue() - previous.getHigh().doubleValue());
                double totalRange = previous.getHigh().doubleValue() - previous.getLow().doubleValue();
                double excessScore = totalRange > 0 ? Math.min((lowerExcess + upperExcess) / totalRange, 0.5) : 0;

                // 综合评分
                return Math.min(0.5 + (engulfingRatio * 0.35) + excessScore, 1.0);
            }
        }

        return 0.0;
    }

    /**
     * 检查RSI强度 (0-1)
     */
    private double checkRsiStrength(List<KLineEntity> klineList) {
        if (klineList.size() < RSI_PERIOD + 3) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算RSI值
        double currentRsi = calculateRSI(closePrices, 0, RSI_PERIOD);
        double previousRsi = calculateRSI(closePrices, 1, RSI_PERIOD);
        double prevPreviousRsi = calculateRSI(closePrices, 2, RSI_PERIOD);

        // 检查是否有过超卖
        double lowestRsi = Math.min(Math.min(currentRsi, previousRsi), prevPreviousRsi);
        boolean wasOversold = lowestRsi < RSI_OVERSOLD;

        // 检查RSI上升趋势
        boolean isRising = currentRsi > previousRsi && previousRsi > prevPreviousRsi;

        if (wasOversold && currentRsi > previousRsi) {
            // 计算从超卖区回升的幅度
            double riseFromOversold = 0.0;
            if (lowestRsi < RSI_OVERSOLD) {
                riseFromOversold = (currentRsi - lowestRsi) / (50.0 - lowestRsi);
                riseFromOversold = Math.min(riseFromOversold, 1.0);
            }

            // 上升速度（当前RSI与前一个的差值）
            double riseSpeed = (currentRsi - previousRsi) / 10.0; // 归一化，假设10点为满分
            riseSpeed = Math.min(riseSpeed, 1.0);

            // 趋势确认（连续上升）
            double trendScore = isRising ? 0.3 : 0.0;

            // 综合评分
            return Math.min(riseFromOversold * 0.5 + riseSpeed * 0.2 + trendScore, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查成交量强度 (0-1)
     */
    private double checkVolumeStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 10) {
            return 0.0;
        }

        KLineEntity current = klineList.get(0);

        // 计算前9根K线的平均成交量
        double avgVolume = 0.0;
        for (int i = 1; i < 10; i++) {
            avgVolume += klineList.get(i).getVolume();
        }
        avgVolume /= 9.0;

        // 当前K线是否为阳线
        boolean currentIsBullish = current.getClose().doubleValue() > current.getOpen().doubleValue();

        if (currentIsBullish && avgVolume > 0) {
            // 计算成交量放大比例
            double volumeRatio = current.getVolume() / avgVolume;

            // 以1.5倍为基准，3倍以上为满分
            double volumeScore = 0.0;
            if (volumeRatio >= 1.5) {
                volumeScore = Math.min((volumeRatio - 1.5) / 1.5, 1.0);
            }

            // 价格实体大小影响
            double bodySize = Math.abs(current.getClose().doubleValue() - current.getOpen().doubleValue());
            double range = current.getHigh().doubleValue() - current.getLow().doubleValue();
            double bodySizeRatio = range > 0 ? bodySize / range : 0;
            double bodyScore = Math.min(bodySizeRatio * 2, 1.0);

            // 综合评分
            return Math.min(volumeScore * 0.7 + bodyScore * 0.3, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查MACD强度 (0-1)
     */
    private double checkMacdStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 50) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算当前和前一个周期的MACD值
        double[] macd = calculateMACD(closePrices);
        double[] prevMacd = calculateMACD(closePrices.subList(1, closePrices.size()));

        double macdLine = macd[0];
        double signalLine = macd[1];
        double histogram = macd[2];
        double prevHistogram = prevMacd[2];

        // 指标强度评分
        double strength = 0.0;

        // 检查金叉信号
        if (macdLine > signalLine && prevMacd[0] <= prevMacd[1]) {
            // 金叉信号强度（取决于交叉角度）
            double crossAngle = (macdLine - signalLine) - (prevMacd[0] - prevMacd[1]);
            double crossStrength = Math.min(crossAngle / 0.001, 1.0); // 归一化，假设0.001为基准变化率
            strength = Math.max(strength, 0.6 + crossStrength * 0.4);
        }

        // 检查柱状图转正
        if (histogram > 0 && prevHistogram <= 0) {
            // 柱状图由负转正的强度
            double histogramStrength = Math.min(histogram / 0.001, 1.0); // 归一化
            strength = Math.max(strength, 0.5 + histogramStrength * 0.3);
        }

        // 检查底部背离
        boolean divergence = checkMacdDivergence(klineList, closePrices);
        if (divergence) {
            // 背离信号通常很强
            strength = Math.max(strength, 0.8);
        }

        return strength;
    }

    /**
     * 检查价格突破强度 (0-1)
     */
    private double checkPriceBreakoutStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 30) {
            return 0.0;
        }

        // 计算10周期简单移动平均线
        List<Double> sma10 = calculateSMA(klineList, 10);
        if (sma10.size() < 5) return 0.0;

        // 当前价格和SMA值
        double currentPrice = klineList.get(0).getClose().doubleValue();
        double smaValue = sma10.get(0);

        // 计算SMA斜率
        double smaSlope = sma10.get(0) - sma10.get(3);

        // 突破强度评分
        double strength = 0.0;

        if (currentPrice > smaValue) {
            // 价格突破幅度
            double breakoutPercentage = (currentPrice - smaValue) / smaValue * 100;
            double breakoutScore = Math.min(breakoutPercentage, 1.0); // 1%突破为满分

            // SMA斜率评分（平缓或向上更好）
            double slopeScore = 0.0;
            if (smaSlope >= 0) {
                // 向上倾斜，满分
                slopeScore = 1.0;
            } else {
                // 下降趋势中减缓，部分得分
                slopeScore = Math.max(0.0, 1.0 + smaSlope * 1000); // 归一化，视下降速度而定
            }

            // K线实体评分（阳线且收盘较高更好）
            KLineEntity currentK = klineList.get(0);
            double bodyRatio = 0.0;
            if (currentK.getClose().doubleValue() > currentK.getOpen().doubleValue()) {
                double range = currentK.getHigh().doubleValue() - currentK.getLow().doubleValue();
                if (range > 0) {
                    bodyRatio = (currentK.getClose().doubleValue() - currentK.getOpen().doubleValue()) / range;
                }
            }
            double bodyScore = Math.min(bodyRatio * 2, 1.0);

            // 综合评分
            strength = breakoutScore * 0.5 + slopeScore * 0.3 + bodyScore * 0.2;
        }

        return strength;
    }

    /**
     * 检查布林带信号强度 (0-1)
     */
    private double checkBollingerBandStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 30) {
            return 0.0;
        }

        int period = 20;
        double devFactor = 2.0;

        // 计算20周期SMA
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        List<Double> sma = new java.util.ArrayList<>();
        for (int i = 0; i < closePrices.size() - period + 1; i++) {
            double sum = 0;
            for (int j = i; j < i + period; j++) {
                sum += closePrices.get(j);
            }
            sma.add(sum / period);
        }

        // 计算标准差
        double sum = 0;
        for (int i = 0; i < period; i++) {
            sum += Math.pow(closePrices.get(i) - sma.get(0), 2);
        }
        double stdDev = Math.sqrt(sum / period);

        // 计算布林带下轨
        double lowerBand = sma.get(0) - (stdDev * devFactor);

        // 信号强度评分
        double strength = 0.0;

        // 检查触及下轨情况
        boolean touchedLowerBand = false;
        int touchIndex = -1;
        for (int i = 1; i < 5 && i < klineList.size(); i++) {
            if (klineList.get(i).getLow().doubleValue() <= lowerBand) {
                touchedLowerBand = true;
                touchIndex = i;
                break;
            }
        }

        if (touchedLowerBand && touchIndex > 0) {
            // 当前K线价格与下轨的距离
            double currentPrice = klineList.get(0).getClose().doubleValue();
            double distanceFromLower = currentPrice - lowerBand;
            double bandWidth = (sma.get(0) + (stdDev * devFactor)) - lowerBand;

            // 反弹幅度评分 (从下轨到中轨之间)
            double bounceRatio = bandWidth > 0 ? distanceFromLower / (bandWidth / 2) : 0;
            double bounceScore = Math.min(bounceRatio, 1.0);

            // 反弹速度（与触及下轨K线的距离越近反弹越迅速）
            double bounceSpeed = 1.0 / (double) touchIndex;

            // 反弹K线形态（阳线且实体大占比高）
            KLineEntity currentK = klineList.get(0);
            boolean isCurrentBullish = currentK.getClose().doubleValue() > currentK.getOpen().doubleValue();
            double bodySize = Math.abs(currentK.getClose().doubleValue() - currentK.getOpen().doubleValue());
            double range = currentK.getHigh().doubleValue() - currentK.getLow().doubleValue();
            double bodySizeRatio = range > 0 ? bodySize / range : 0;

            // K线形态得分
            double candleScore = isCurrentBullish ? bodySizeRatio : 0;

            // 布林带收缩程度（窄带扩张信号更强） - 修复：添加边界检查
            double prevBandWidth = 0;
            if (sma.size() > 5) {
                // 计算5个周期前的标准差
                double prevSum = 0;
                int count = 0;
                for (int i = 5; i < 5 + period && i < closePrices.size() && i < sma.size(); i++) {
                    prevSum += Math.pow(closePrices.get(i) - sma.get(5), 2);
                    count++;
                }
                if (count > 0) {
                    double prevStdDev = Math.sqrt(prevSum / count);
                    prevBandWidth = prevStdDev * 2 * devFactor;
                }
            }

            // 带宽收缩评分
            double bandwidthScore = 0.0;
            if (prevBandWidth > 0) {
                double bandwidthRatio = (stdDev * 2 * devFactor) / prevBandWidth;
                bandwidthScore = bandwidthRatio < 1.0 ? (1.0 - bandwidthRatio) : 0.0;
            }

            // 综合评分
            strength = bounceScore * 0.4 + bounceSpeed * 0.2 + candleScore * 0.2 + bandwidthScore * 0.2;
        }

        return strength;
    }

    /**
     * 计算简单移动平均线
     */
    private List<Double> calculateSMA(List<KLineEntity> klineList, int period) {
        List<Double> smaValues = new java.util.ArrayList<>();

        for (int i = 0; i < klineList.size() - period + 1; i++) {
            double sum = 0;
            for (int j = i; j < i + period; j++) {
                sum += klineList.get(j).getClose().doubleValue();
            }
            smaValues.add(sum / period);
        }

        return smaValues;
    }

    /**
     * 处理入场操作
     */
    private void processEntry(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        try {
            log.info("准备入场: instId={}, 当前价格={}", instId, currentPrice);

            // 计算止盈止损价格
            StopLevelDTO stopLevels = calculateStopLevels(instId, currentPrice, klineList);
            if (stopLevels == null) {
                return;
            }

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());
            log.info("入场参数: 交易对={}, 价格={}, 数量={}, 止损价={}, 止盈价1={}, 止盈价2={}",
                instId, currentPrice, sz, stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2);

            // 执行开仓
            trade(instId, "buy", sz, "long", false);
            ThreadUtil.sleep(100);

            // 设置止盈止损
            setupStopOrders(instId, currentPrice, stopLevels);

        } catch (Exception e) {
            log.error("执行入场操作异常", e);
        }
    }

    /**
     * 计算止盈止损价格
     */
    private StopLevelDTO calculateStopLevels(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        // 计算ATR用于设置止损和止盈
        double atr = calculateATR(klineList, 0, ATR_PERIOD);
        if (atr <= 0) {
            log.info("ATR计算错误，取消入场");
            return null;
        }

        // 计算EMA55作为止损基准线
//        List<Double> closePrices = klineList.stream()
//            .map(k -> k.getClose().doubleValue())
//            .collect(java.util.stream.Collectors.toList());
//        double emaSlow = calculateEMA(closePrices, 0, EMA_SLOW);

        // 设置止损价 (3倍ATR)
        BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() - atr * STOP_LOSS_ATR_MULTIPLIER)
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 计算止盈价格（基于ATR倍数）
        BigDecimal takeProfitPrice1 = currentPrice.add(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_1))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal takeProfitPrice2 = currentPrice.add(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_2))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        return new StopLevelDTO(stopLossPrice, takeProfitPrice1, takeProfitPrice2);
    }

    /**
     * 设置止盈止损订单
     */
    private void setupStopOrders(String instId, BigDecimal currentPrice, StopLevelDTO stopLevels) {
        // 获取持仓信息
        Position pos = getPos(instId);
        if (pos == null) {
            log.info("未获取到持仓信息，无法设置止盈止损");
            return;
        }

        log.info("开仓成功: 交易对={}, 持仓ID={}, 数量={}", instId, pos.getPosId(), pos.getAvailPos());

        // 计算各止盈档位的仓位大小
        BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
        BigDecimal tp1Size = totalPos.multiply(BigDecimal.valueOf(0.5)) // 第一档平仓50%
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal tp2Size = totalPos.subtract(tp1Size) // 第二档平仓剩余50%
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 设置止损单
        String stopLossPx = stopLevels.stopLossPrice.toString();
        algoTradeLoss(instId, "sell", totalPos.toString(), "long", stopLossPx);
        log.info("止损单设置成功: 交易对={}, 价格={}, 数量={}", instId, stopLossPx, totalPos);

        // 设置止盈单
        String tp1Px = stopLevels.takeProfitPrice1.toString();
        String tp2Px = stopLevels.takeProfitPrice2.toString();

        algoTradeWin(instId, "sell", getMaxSellableSz(instId, tp1Size.doubleValue()), "long", tp1Px);
        log.info("止盈单1设置成功: 交易对={}, 价格={}, 数量={}", instId, tp1Px, tp1Size);

        algoTradeWin(instId, "sell", getMaxSellableSz(instId, tp2Size.doubleValue()), "long", tp2Px);
        log.info("止盈单2设置成功: 交易对={}, 价格={}, 数量={}", instId, tp2Px, tp2Size);

        // 发送消息通知
        messageService.send(instId + "多单策略", MessageFormat.format("{0}多头入场: 入场价={1}, 止损价={2}, 止盈1={3}, 止盈2={4}",
            instId, new BigDecimal(pos.getAvgPx()).setScale(currentPrice.scale(), RoundingMode.DOWN), stopLevels.stopLossPrice, stopLevels.takeProfitPrice1,
            stopLevels.takeProfitPrice2
        ));

        // 记录交易信息到Redis
        String key = STRATEGY_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(12));
    }

    /**
     * 计算EMA指标
     */
    private double calculateEMA(List<Double> prices, int offset, int period) {
        if (prices.size() < offset + period) {
            return 0;
        }

        double alpha = 2.0 / (period + 1);
        double ema = prices.get(offset + period - 1);

        for (int i = offset + period - 2; i >= offset; i--) {
            ema = alpha * prices.get(i) + (1 - alpha) * ema;
        }

        return ema;
    }

    /**
     * 计算ATR指标
     */
    private double calculateATR(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }

        double[] trValues = new double[period];

        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;

            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);

            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            trValues[i] = Math.max(Math.max(tr1, tr2), tr3);
        }

        // 计算ATR - 简单移动平均
        double sum = 0;
        for (double tr : trValues) {
            sum += tr;
        }

        return sum / period;
    }

    /**
     * 检查是否已有交易
     */
    private boolean checkTrade(String key) {
        try {
            Object pos = com.miner.common.utils.redis.RedisUtils.getCacheObject(key);
            if (pos != null) {
                return true;
            }
        } catch (Exception e) {
            log.error("检查交易状态异常", e);
        }
        return false;
    }

    /**
     * 计算MACD指标
     *
     * @param prices 价格列表
     * @return double数组，[0]=MACD线，[1]=信号线，[2]=直方图
     */
    private double[] calculateMACD(List<Double> prices) {
        // MACD默认参数 (12,26,9)
        int fastLength = 12;
        int slowLength = 26;
        int signalLength = 9;

        // 计算快速EMA
        double fastEMA = calculateEMA(prices, 0, fastLength);

        // 计算慢速EMA
        double slowEMA = calculateEMA(prices, 0, slowLength);

        // 计算MACD线 (DIF)
        double macdLine = fastEMA - slowEMA;

        // 计算信号线 (DEA) - MACD的9日EMA
        List<Double> macdValues = new java.util.ArrayList<>();
        for (int i = 0; i < Math.min(signalLength, prices.size()); i++) {
            double fastEMAi = calculateEMA(prices, i, fastLength);
            double slowEMAi = calculateEMA(prices, i, slowLength);
            macdValues.add(fastEMAi - slowEMAi);
        }

        double signalLine = calculateEMA(macdValues, 0, signalLength);

        // 计算柱状图 (MACD Histogram)
        double histogram = macdLine - signalLine;

        return new double[]{macdLine, signalLine, histogram};
    }

    /**
     * 计算RSI指标
     *
     * @param prices 价格列表
     * @param offset 偏移量
     * @param period RSI周期
     * @return RSI值
     */
    private double calculateRSI(List<Double> prices, int offset, int period) {
        if (prices.size() < period + offset + 1) {
            return 50.0; // 默认返回中性值
        }

        double sumGain = 0.0;
        double sumLoss = 0.0;

        // 从偏移位置开始，计算period个周期的涨跌幅
        for (int i = offset + 1; i <= offset + period; i++) {
            double change = prices.get(i - 1) - prices.get(i);
            if (change > 0) {
                sumGain += change;
            } else {
                sumLoss -= change;
            }
        }

        // 防止除以0的情况
        if (sumLoss == 0) {
            return 100.0;
        }

        // 计算相对强弱值
        double rs = sumGain / sumLoss;

        // 计算RSI值
        return 100.0 - (100.0 / (1.0 + rs));
    }

    /**
     * 检查MACD底部背离
     * 当价格创新低但MACD不创新低时，可能出现底部背离
     *
     * @param klineList   K线数据
     * @param closePrices 收盘价列表
     * @return 是否存在底部背离
     */
    private boolean checkMacdDivergence(List<KLineEntity> klineList, List<Double> closePrices) {
        if (klineList.size() < 20) {
            return false;
        }

        // 寻找最近的两个低点
        int low1Index = -1;
        int low2Index = -1;

        for (int i = 5; i < klineList.size() - 5; i++) {
            // 判断是否为低点 (简单判断：前后各两根K线的价格都比当前价格高)
            if (klineList.get(i - 2).getLow().doubleValue() > klineList.get(i).getLow().doubleValue() &&
                klineList.get(i - 1).getLow().doubleValue() > klineList.get(i).getLow().doubleValue() &&
                klineList.get(i + 1).getLow().doubleValue() > klineList.get(i).getLow().doubleValue() &&
                klineList.get(i + 2).getLow().doubleValue() > klineList.get(i).getLow().doubleValue()) {

                if (low1Index == -1) {
                    low1Index = i;
                } else {
                    low2Index = low1Index;
                    low1Index = i;
                    break;
                }
            }
        }

        if (low1Index != -1 && low2Index != -1) {
            // 检查价格是否创新低
            boolean priceNewLow = klineList.get(low1Index).getLow().doubleValue() <
                klineList.get(low2Index).getLow().doubleValue();

            if (priceNewLow) {
                // 检查MACD是否创新低
                double macdLow1 = calculateMACD(closePrices.subList(0, closePrices.size() - low1Index))[0];
                double macdLow2 = calculateMACD(closePrices.subList(0, closePrices.size() - low2Index))[0];

                // 如果价格创新低但MACD不创新低，则出现底部背离
                return macdLow1 > macdLow2;
            }
        }

        return false;
    }

    /**
     * 动态支撑区域结果类
     * 封装基于ATR波动率计算的EMA55动态支撑区域的各项参数和结果
     * 用于在策略中判断价格是否在合适的支撑位置，并提供详细的计算参数用于日志记录和分析
     */
    private static class SupportZoneResult {
        /**
         * 价格是否在动态计算的支撑区域内
         * true表示在支撑区域内，可能是合适的入场机会
         */
        private final boolean inSupportZone;

        /**
         * ATR波动率占价格的百分比
         * 例如: 2.5表示ATR值约为价格的2.5%，反映市场当前的波动程度
         */
        private final double atrPercent;

        /**
         * 支撑区域的最小距离百分比
         * 价格需要至少低于EMA55这个百分比才被视为在支撑区
         * 在低波动市场下约为0.5%-1.5%，高波动市场下会更大
         */
        private final double minDistancePercent;

        /**
         * 支撑区域的最大距离百分比
         * 价格低于EMA55不超过此百分比才被视为在支撑区
         * 超过此值可能表示趋势已发生变化，不适合做支撑性入场
         */
        private final double maxDistancePercent;

        /**
         * 价格实际低于EMA55的百分比
         * 正值表示价格在EMA55下方，负值表示价格在EMA55上方
         */
        private final double actualDistancePercent;

        /**
         * 构造动态支撑区域结果对象
         *
         * @param inSupportZone         是否在支撑区域内
         * @param atrPercent            ATR占价格百分比
         * @param minDistancePercent    支撑区最小距离
         * @param maxDistancePercent    支撑区最大距离
         * @param actualDistancePercent 实际距离百分比
         */
        public SupportZoneResult(boolean inSupportZone, double atrPercent, double minDistancePercent,
                                 double maxDistancePercent, double actualDistancePercent) {
            this.inSupportZone = inSupportZone;
            this.atrPercent = atrPercent;
            this.minDistancePercent = minDistancePercent;
            this.maxDistancePercent = maxDistancePercent;
            this.actualDistancePercent = actualDistancePercent;
        }

        /**
         * 判断价格是否在动态支撑区域内
         *
         * @return true如果在支撑区域内，否则false
         */
        public boolean isInSupportZone() {
            return inSupportZone;
        }

        /**
         * 获取ATR占价格的百分比，表示市场波动程度
         *
         * @return ATR百分比值
         */
        public double getAtrPercent() {
            return atrPercent;
        }

        /**
         * 获取支撑区域的最小距离百分比
         *
         * @return 最小距离百分比
         */
        public double getMinDistancePercent() {
            return minDistancePercent;
        }

        /**
         * 获取支撑区域的最大距离百分比
         *
         * @return 最大距离百分比
         */
        public double getMaxDistancePercent() {
            return maxDistancePercent;
        }

        /**
         * 获取价格与EMA55的实际距离百分比
         *
         * @return 实际距离百分比
         */
        public double getActualDistancePercent() {
            return actualDistancePercent;
        }
    }

    /**
     * 用于存储止盈止损价格的内部类
     */
    private static class StopLevelDTO {
        private final BigDecimal stopLossPrice;
        private final BigDecimal takeProfitPrice1;
        private final BigDecimal takeProfitPrice2;

        public StopLevelDTO(BigDecimal stopLossPrice, BigDecimal takeProfitPrice1, BigDecimal takeProfitPrice2) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice1 = takeProfitPrice1;
            this.takeProfitPrice2 = takeProfitPrice2;
        }
    }

    public void setLever() {
        AccountAPIService accountAPIService = new AccountAPIServiceImpl();
        SellStrategy bean = com.miner.common.utils.spring.SpringUtils.getBean(SellStrategy.class);
        final List<String> instIds = bean.getInstIds(200);
        for (String instId : instIds) {
            SetLeverage setLeverage = new SetLeverage();
            setLeverage.setInstId(instId);
            setLeverage.setLever("20");
            setLeverage.setMgnMode("cross");

            JSONObject jsonObject = accountAPIService.setLeverage(setLeverage);
            log.info("{} set level ,{}", instId, jsonObject.toJSONString());
            cn.hutool.core.thread.ThreadUtil.sleep(50);
        }
    }

    /**
     * 计算组合指标得分
     * 基于多个指标的加权平均，并且仅当指标超过各自阈值时才计入得分
     *
     * @param strengths  各指标强度
     * @param weights    各指标权重
     * @param thresholds 各指标阈值
     * @return 组合得分（0-1范围）
     */
    private double calculateCombinedScore(double[] strengths, double[] weights, double[] thresholds) {
        if (strengths.length == 0 || strengths.length != weights.length || strengths.length != thresholds.length) {
            return 0.0;
        }

        double totalScore = 0.0;
        double totalWeight = 0.0;
        int validSignalCount = 0;

        for (int i = 0; i < strengths.length; i++) {
            // 只有超过阈值的指标才计入得分
            if (strengths[i] >= thresholds[i]) {
                totalScore += strengths[i] * weights[i];
                totalWeight += weights[i];
                validSignalCount++;
            }
        }

        // 至少需要一个有效信号
        if (validSignalCount == 0 || totalWeight == 0.0) {
            return 0.0;
        }

        // 根据有效信号数量给予额外加分
        double signalDiversityBonus = Math.min((double) validSignalCount / strengths.length, 1.0) * 0.1;

        // 返回归一化后的分数 + 多信号额外奖励分
        return Math.min((totalScore / totalWeight) + signalDiversityBonus, 1.0);
    }

    /**
     * 计算技术指标组合得分
     * 包括MACD、RSI和布林带
     */
    private double calculateTechnicalIndicatorScore(double macdStrength, double rsiStrength, double bollingerStrength) {
        return calculateCombinedScore(
            new double[]{macdStrength, rsiStrength, bollingerStrength},
            new double[]{0.4, 0.4, 0.2},
            new double[]{COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD}
        );
    }

    /**
     * 计算价格形态组合得分
     * 包括K线形态和价格突破
     */
    private double calculatePricePatternScore(double bullishPatternStrength, double priceBreakoutStrength) {
        return calculateCombinedScore(
            new double[]{bullishPatternStrength, priceBreakoutStrength},
            new double[]{0.5, 0.5},
            new double[]{COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD}
        );
    }

    /**
     * 计算量价关系组合得分
     * 包括成交量、量能突破、OBV趋势和OBV背离
     */
    private double calculateVolumePriceScore(double volumeStrength, VolumePriceAnalysisResult vpResult) {
        return calculateCombinedScore(
            new double[]{
                volumeStrength,
                vpResult.getVolumeBreakoutStrength(),
                vpResult.getObvTrendStrength(),
                vpResult.getObvDivergenceStrength()
            },
            new double[]{0.3, 0.2, 0.2, 0.3},
            new double[]{
                COMBINED_VOLUME_THRESHOLD,
                COMBINED_VOLUME_THRESHOLD,
                COMBINED_VOLUME_THRESHOLD,
                COMBINED_VOLUME_THRESHOLD
            }
        );
    }

    /**
     * 计算趋势确认组合得分
     * 跨类型组合：MACD、价格突破和量能突破
     */
    private double calculateTrendConfirmationScore(double macdStrength, double priceBreakoutStrength, double volumeBreakoutStrength) {
        return calculateCombinedScore(
            new double[]{macdStrength, priceBreakoutStrength, volumeBreakoutStrength},
            new double[]{0.4, 0.3, 0.3},
            new double[]{COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD, COMBINED_VOLUME_THRESHOLD}
        );
    }

    /**
     * 计算底部反转组合得分
     * 跨类型组合：RSI、K线形态和OBV背离
     */
    private double calculateBottomReversalScore(double rsiStrength, double bullishPatternStrength, double obvDivergenceStrength) {
        return calculateCombinedScore(
            new double[]{rsiStrength, bullishPatternStrength, obvDivergenceStrength},
            new double[]{0.4, 0.35, 0.25},
            new double[]{COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD, COMBINED_SIGNAL_THRESHOLD}
        );
    }

    /**
     * 格式化组合得分日志信息
     */
    private String formatCombinedScoreLog(Map<String, Double> combinedScores) {
        StringBuilder log = new StringBuilder();
        for (Map.Entry<String, Double> entry : combinedScores.entrySet()) {
            log.append(entry.getKey())
                .append("=")
                .append(String.format("%.2f", entry.getValue()))
                .append(" ");
        }
        return log.toString();
    }

    /**
     * 根据市场环境确定组合信号阈值
     *
     * @param dynamicThreshold 动态阈值，反映市场状况
     * @return 适合当前市场的组合信号阈值
     */
    private double getCombinedScoreThreshold(double dynamicThreshold) {
        // 当前baseThreshold为3.8，动态阈值范围在±0.8之内
        // 根据市场环境动态调整组合信号阈值
        if (dynamicThreshold > 4.2) {  // baseThreshold + 0.4 (严格环境)
            // 在严格市场环境下，提高组合信号阈值
            return 0.7;
        } else if (dynamicThreshold < 3.4) {  // baseThreshold - 0.4 (宽松环境)
            // 在宽松市场环境下，降低组合信号阈值
            return 0.6;
        } else {
            // 正常市场环境，使用标准阈值
            return 0.65;
        }
    }

    /**
     * 判断是否有任一组合信号强度超过阈值
     */
    private boolean hasAnyCombinedSignalAboveThreshold(Map<String, Double> combinedScores, double threshold) {
        return combinedScores.values().stream()
            .anyMatch(score -> score >= threshold);
    }

    /**
     * 根据信号类型调整最终阈值
     * 强信号可适当降低总分要求，仅有组合信号则提高要求
     */
    private double adjustFinalThreshold(double baseThreshold, boolean hasStrongSignal, boolean hasCombinedSignal) {
        // 在总分较高的情况下，能更宽松地使用组合信号；在总分较低时，需要更严格的强信号
        double finalThreshold = baseThreshold;

        if (hasStrongSignal) {
            // 如果有强信号，可以稍微降低总分要求
            finalThreshold *= 0.9;
        } else if (hasCombinedSignal) {
            // 仅有组合信号时，略微提高总分要求
            finalThreshold *= 1.05;
        }

        return finalThreshold;
    }

    /**
     * 记录入场信号决策过程
     */
    private void logEntrySignalDecision(String instId, double score, double threshold,
                                        boolean hasStrongSignal, boolean hasCombinedSignal,
                                        boolean entrySignal) {
        if (entrySignal) {
            // 信号触发，记录详情
            log.info("{} 入场信号触发: 总分={}/10 阈值={} 强信号={} 组合信号={}",
                instId,
                String.format("%.1f", score),
                String.format("%.1f", threshold),
                hasStrongSignal ? "是" : "否",
                hasCombinedSignal ? "是" : "否"
            );
        } else if (score >= threshold * 0.8) {
            // 接近触发，记录详情
            log.debug("{} 接近入场信号: 总分={}/10 阈值={} 强信号={} 组合信号={}",
                instId,
                String.format("%.1f", score),
                String.format("%.1f", threshold),
                hasStrongSignal ? "是" : "否",
                hasCombinedSignal ? "是" : "否"
            );
        }
    }

    /**
     * 寻找新的符合做空条件的候选交易对
     */
    private void findShortCandidates(String instId) {
        try {
            // 检查是否已有仓位
            if (checkTrade(SHORT_STRATEGY_KEY_PREFIX + instId)) {
                return;
            }

            // 检查是否已经是做空候选交易对
            if (isShortCandidateInst(instId)) {
                return;
            }

            // 获取主周期K线数据
            List<KLineEntity> kline4h = getKline(instId, MAIN_TIMEFRAME, "100");
            if (kline4h.isEmpty()) {
                return;
            }

            // 计算当前价格
            BigDecimal currentPrice = kline4h.get(0).getClose();
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 提取收盘价
            List<Double> closePrices = kline4h.stream()
                .map(k -> k.getClose().doubleValue())
                .collect(java.util.stream.Collectors.toList());

            // 计算EMA指标
            double emaMid = calculateEMA(closePrices, 0, EMA_MID);
            double emaSlow = calculateEMA(closePrices, 0, EMA_SLOW);

            // 检查EMA55斜率
            double emaSlowSlope = calculateEmaSlope(kline4h);

            // 做空条件：EMA55斜率为负且EMA55 > EMA21（空头排列）且价格高于EMA55
            if (emaSlowSlope < EMA_SLOPE_THRESHOLD && emaSlow > emaMid &&
                currentPrice.doubleValue() > emaSlow) {

                // 检查价格是否在做空区域（高于EMA55一定比例）
                if (isPriceInShortZone(currentPrice.doubleValue(), emaSlow, kline4h)) {
                    // 记录做空候选交易对
                    log.info("发现符合做空条件的交易对: {}, 当前价格: {}, EMA55: {}, EMA55斜率: {}%",
                        instId, currentPrice, String.format("%.4f", emaSlow),
                        String.format("%.2f", emaSlowSlope * 100));

                    saveShortCandidateInst(instId);

                    // 发送提醒消息
//                    messageService.send(instId + "做空区间提醒",
//                        MessageFormat.format("{0} 已进入做空区间，当前价格: {1}, EMA55: {2}",
//                            instId, currentPrice, String.format("%.4f", emaSlow)));
                }
            }
        } catch (Exception e) {
            log.error("处理做空候选交易对 {} 发生异常", instId, e);
        }
    }

    /**
     * 检查价格是否在做空区域（高于EMA55一定比例）
     */
    private boolean isPriceInShortZone(double price, double ema55, List<KLineEntity> klineList) {
        // 计算ATR用于动态调整距离阈值
        double atr = calculateATR(klineList, 0, ATR_PERIOD);
        double atrPercent = (atr / price) * 100;

        // 计算价格与EMA55的距离百分比
        double distance = (price - ema55) / ema55 * 100;

        // 动态阈值：基础值0.5% + ATR贡献
        double minDistanceThreshold = 0.5 + (atrPercent * 0.5);
        double maxDistanceThreshold = Math.min(1.5 + (atrPercent * 0.8), 5.0);

        // 价格需要高于EMA55且距离在阈值范围内
        return distance > minDistanceThreshold && distance < maxDistanceThreshold;
    }

    /**
     * 保存做空候选交易对到Redis
     */
    private void saveShortCandidateInst(String instId) {
        String key = SHORT_CANDIDATE_KEY_PREFIX + instId;
        // 修复：使用常量定义的过期时间
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, System.currentTimeMillis(), Duration.ofHours(CANDIDATE_EXPIRE_HOURS));
    }

    /**
     * 检查是否为做空候选交易对
     */
    private boolean isShortCandidateInst(String instId) {
        String key = SHORT_CANDIDATE_KEY_PREFIX + instId;
        return com.miner.common.utils.redis.RedisUtils.getCacheObject(key) != null;
    }

    /**
     * 移除做空候选交易对
     */
    private void removeShortCandidateInst(String instId) {
        String key = SHORT_CANDIDATE_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.deleteObject(key);
    }

    /**
     * 检查做空候选交易对是否满足入场条件
     */
    private void checkShortCandidateEntry(String instId) {
        try {
            // 检查是否为做空候选交易对
            if (!isShortCandidateInst(instId)) {
                return;
            }

            // 检查是否已有仓位
            if (checkTrade(SHORT_STRATEGY_KEY_PREFIX + instId) || getPos(instId) != null) {
                // 已有仓位，移除候选状态
                removeShortCandidateInst(instId);
                return;
            }

            // 获取K线数据
            List<KLineEntity> kline4h = getKline(instId, MAIN_TIMEFRAME, "100");
            List<KLineEntity> kline5m = getKline(instId, SUB_TIMEFRAME, "100");

            if (kline4h.isEmpty() || kline5m.isEmpty()) {
                return;
            }

            // 计算当前价格
            BigDecimal currentPrice = kline5m.get(0).getClose();
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 再次确认EMA空头排列条件
            List<Double> closePrices = kline4h.stream()
                .map(k -> k.getClose().doubleValue())
                .collect(java.util.stream.Collectors.toList());

            double emaMid = calculateEMA(closePrices, 0, EMA_MID);
            double emaSlow = calculateEMA(closePrices, 0, EMA_SLOW);
            double emaSlowSlope = calculateEmaSlope(kline4h);

            if (emaSlowSlope >= EMA_SLOPE_THRESHOLD || emaSlow <= emaMid) {
                // 不再满足EMA空头排列条件，移除候选状态
                removeShortCandidateInst(instId);
                return;
            }

            // 通过小周期确认做空入场信号 - 使用重构后的信号评估服务
            SignalEvaluationService.SignalEvaluationResult result = signalEvaluationService.evaluateShortSignal(kline5m);

            // 计算动态阈值
            double baseThreshold = 3.9;
            double dynamicThreshold = calculateDynamicThreshold(kline5m, baseThreshold);

            // 判断是否满足入场条件
            boolean entrySignal = result.getScoreRatio() >= dynamicThreshold;

            // 强信号判定（如果有强信号组合，可以降低总体阈值）
            if (result.hasStrongSignal() && result.getScoreRatio() >= dynamicThreshold * 0.85) {
                entrySignal = true;
                log.info("{} 强信号组合触发，降低入场阈值: {}", instId, String.format("%.1f", dynamicThreshold * 0.85));
            }

            // 额外安全检查 - 即使评分达标，如果ADX过低且没有强信号，也不入场
            double adxStrength = result.getSignalStrength("ADX_SHORT");
            if (entrySignal && adxStrength < 0.25 && !result.hasStrongSignal()) {
                entrySignal = false;
                log.info("{} 评分达标但ADX过低且无强信号组合，取消入场", instId);
            }

            if (entrySignal) {
                log.info("候选做空交易对 {} 满足小周期确认条件，准备入场", instId);

                // 执行做空入场操作 - 使用重构后的交易执行服务
                boolean success = tradeExecutionService.executeShortEntry(instId, currentPrice, kline4h);

                if (success) {
                    // 入场成功后移除候选状态
                    removeShortCandidateInst(instId);
                }
            }
        } catch (Exception e) {
            log.error("检查做空候选交易对 {} 入场条件时发生异常", instId, e);
        }
    }

    /**
     * 计算ADX指标
     *
     * @param klineList K线数据
     * @param offset    偏移量
     * @param period    ADX周期
     * @return ADX值
     */
    private double calculateADX(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return 0.0;
        }

        // 计算+DI和-DI
        double[] diValues = calculateDI(klineList, offset, period);
        double plusDI = diValues[0];
        double minusDI = diValues[1];

        // 计算方向指数DX
        double dx = 0.0;
        if (plusDI + minusDI > 0) {
            dx = 100 * Math.abs(plusDI - minusDI) / (plusDI + minusDI);
        }

        // 计算ADX（DX的平滑平均）
        double adx = dx;
        for (int i = 1; i < period; i++) {
            if (klineList.size() <= offset + i + period) {
                break;
            }
            double[] prevDI = calculateDI(klineList, offset + i, period);
            double prevPlusDI = prevDI[0];
            double prevMinusDI = prevDI[1];

            double prevDX = 0.0;
            if (prevPlusDI + prevMinusDI > 0) {
                prevDX = 100 * Math.abs(prevPlusDI - prevMinusDI) / (prevPlusDI + prevMinusDI);
            }

            adx = ((period - 1) * adx + prevDX) / period;
        }

        return adx;
    }

    /**
     * 计算方向指标DI
     *
     * @return double数组，[0]=+DI，[1]=-DI
     */
    private double[] calculateDI(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return new double[]{0.0, 0.0};
        }

        double sumTR = 0.0;
        double sumPlusDM = 0.0;
        double sumMinusDM = 0.0;

        for (int i = offset; i < offset + period; i++) {
            KLineEntity current = klineList.get(i);
            KLineEntity previous = klineList.get(i + 1);

            // 计算真实波幅TR
            double high = current.getHigh().doubleValue();
            double low = current.getLow().doubleValue();
            double prevClose = previous.getClose().doubleValue();

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(Math.max(tr1, tr2), tr3);
            sumTR += tr;

            // 计算方向变动DM
            double upMove = high - previous.getHigh().doubleValue();
            double downMove = previous.getLow().doubleValue() - low;

            // +DM: 如果上涨变动 > 下跌变动且上涨变动 > 0
            if (upMove > downMove && upMove > 0) {
                sumPlusDM += upMove;
            }
            // -DM: 如果下跌变动 > 上涨变动且下跌变动 > 0
            else if (downMove > upMove && downMove > 0) {
                sumMinusDM += downMove;
            }
        }

        // 防止除以0
        if (sumTR == 0) {
            return new double[]{0.0, 0.0};
        }

        // 计算+DI和-DI
        double plusDI = 100 * sumPlusDM / sumTR;
        double minusDI = 100 * sumMinusDM / sumTR;

        return new double[]{plusDI, minusDI};
    }

    /**
     * 做空入场信号确认
     *
     * @param kline5m 5分钟K线数据
     * @return true 满足做空入场条件，false 不满足
     */
    private boolean confirmShortEntrySignal(List<KLineEntity> kline5m) {
        // 快速验证数据有效性
        if (kline5m == null || kline5m.size() < VOLUME_ANALYSIS_MIN_BARS) {
            log.debug("K线数据不足，无法进行做空信号确认分析");
            return false;
        }

        String instId = kline5m.get(0).getInstId();

        // 1. 预先计算一些共用的数据，避免重复计算
        List<Double> closePrices = kline5m.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 2. 计算ADX指标 - 优先评估趋势强度（做空方向）
        double adxStrength = checkADXStrengthForShort(kline5m);

        // 如果ADX极低(<15)，说明无明显趋势，快速返回
        if (adxStrength < 0.3) {
            log.debug("{} ADX强度过低({}), 无明显趋势，跳过做空信号确认", instId, String.format("%.2f", adxStrength));
            return false;
        }

        // 3. 计算常规信号强度（0-1范围）- 做空版本
        double bearishPatternStrength = checkBearishCandlePatternStrength(kline5m); // 看跌K线形态
        double rsiStrength = checkRsiOverboughtStrength(kline5m); // RSI超买回落
        double macdStrength = checkMacdShortStrength(kline5m); // MACD死叉/背离

        // 4. 快速检查核心指标 - 如果核心指标全部较弱，快速返回
        if (bearishPatternStrength < 0.3 && rsiStrength < 0.3 && macdStrength < 0.3 && adxStrength < 0.4) {
            log.debug("{} 核心指标全部较弱，跳过进一步分析", instId);
            return false;
        }

        // 5. 计算其他信号强度
        double volumeStrength = checkShortVolumeStrength(kline5m); // 做空成交量特征
        double priceBreakdownStrength = checkPriceBreakdownStrength(kline5m); // 价格跌破
        double bollingerStrength = checkBollingerBandShortStrength(kline5m); // 布林带做空信号

        // 6. 量价关系分析结果
        VolumePriceAnalysisResult vpResult = analyzeVolumePriceForShort(kline5m);

        // 7. 日志输出所有信号强度及量价分析
        log.info("{} 做空小周期确认: ADX={}, K线形态={}, RSI={}, MACD={}, 价格跌破={}, 布林带={}, 成交量={}, 量价分析:{}",
            instId,
            String.format("%.2f", adxStrength),
            String.format("%.2f", bearishPatternStrength),
            String.format("%.2f", rsiStrength),
            String.format("%.2f", macdStrength),
            String.format("%.2f", priceBreakdownStrength),
            String.format("%.2f", bollingerStrength),
            String.format("%.2f", volumeStrength),
            vpResult.getLogInfo()
        );

        // 各信号权重 - 优化做空策略的权重分配
        double macdWeight = 2.0;
        double bollingerWeight = 1.8;  // 提高布林带权重至1.8
        double rsiWeight = 2.0;
        double priceBreakdownWeight = 2.0;  // 提高价格跌破权重至2.0
        double candlePatternWeight = 1.5;
        double volumeWeight = 1.5;  // 提高成交量权重至1.5
        double adxWeight = 2.2;  // 适当降低ADX权重至2.2
        double volumeBreakoutWeight = 1.0;
        double volumeShrinkWeight = 0.7;
        double obvTrendWeight = 0.8;
        double obvBreakoutWeight = 0.8;
        double obvDivergenceWeight = 1.5;  // 提高OBV背离权重至1.5

        // 加权分数累加
        double totalScore = 0.0;
        totalScore += macdStrength * macdWeight;
        totalScore += bollingerStrength * bollingerWeight;
        totalScore += rsiStrength * rsiWeight;
        totalScore += priceBreakdownStrength * priceBreakdownWeight;
        totalScore += bearishPatternStrength * candlePatternWeight;
        totalScore += volumeStrength * volumeWeight;
        totalScore += adxStrength * adxWeight; // ADX得分
        totalScore += vpResult.getVolumeBreakoutStrength() * volumeBreakoutWeight;
        totalScore += vpResult.getVolumeShrinkStrength() * volumeShrinkWeight;
        totalScore += vpResult.getObvTrendStrength() * obvTrendWeight;
        totalScore += vpResult.getObvBreakoutStrength() * obvBreakoutWeight;
        totalScore += vpResult.getObvDivergenceStrength() * obvDivergenceWeight;

        // 最大可能分数
        double maxPossibleScore = macdWeight + bollingerWeight + rsiWeight + priceBreakdownWeight + candlePatternWeight +
            volumeWeight + adxWeight + volumeBreakoutWeight + volumeShrinkWeight + obvTrendWeight + obvBreakoutWeight + obvDivergenceWeight;

        // 评分比例（满分10分）
        double scoreRatio = (totalScore / maxPossibleScore) * 10.0;

        // 构建详细日志
        String detailLog = String.format(
            "(ADX=%s×%s, MACD=%s×%s, RSI=%s×%s, 价格跌破=%s×%s, K线形态=%s×%s, 布林带=%s×%s, 成交量=%s×%s)",
            String.format("%.2f", adxStrength), String.format("%.1f", adxWeight),
            String.format("%.2f", macdStrength), String.format("%.1f", macdWeight),
            String.format("%.2f", rsiStrength), String.format("%.1f", rsiWeight),
            String.format("%.2f", priceBreakdownStrength), String.format("%.1f", priceBreakdownWeight),
            String.format("%.2f", bearishPatternStrength), String.format("%.1f", candlePatternWeight),
            String.format("%.2f", bollingerStrength), String.format("%.1f", bollingerWeight),
            String.format("%.2f", volumeStrength), String.format("%.1f", volumeWeight)
        );

        // 9. 记录评分详情
        log.info("{} 做空反转强度评分: {}/{} = {}/10, {}",
            instId,
            String.format("%.1f", totalScore),
            String.format("%.1f", maxPossibleScore),
            String.format("%.1f", scoreRatio),
            detailLog
        );

        // 10. 动态阈值（根据市场状态自适应）
        double baseThreshold = 3.9; // 做空阈值适当降低，但仍比做多(3.8)略严格
        double dynamicThreshold = calculateDynamicThreshold(kline5m, baseThreshold);

        // 11. 强信号组合检测 - 提高阈值要求
        boolean hasStrongSignal = false;

        // 强信号组合1: ADX强 + MACD死叉 + RSI超买回落 (适当降低阈值要求)
        if (adxStrength > 0.6 && macdStrength > SHORT_SIGNAL_THRESHOLD && rsiStrength > SHORT_SIGNAL_THRESHOLD) {
            hasStrongSignal = true;
            log.info("{} 强信号组合1触发: ADX强 + MACD死叉 + RSI超买回落", instId);
        }

        // 强信号组合2: 看跌K线形态强 + 价格跌破 + 成交量配合 (适当降低阈值要求)
        if (bearishPatternStrength > 0.6 && priceBreakdownStrength > SHORT_SIGNAL_THRESHOLD && volumeStrength > SHORT_SIGNAL_THRESHOLD) {
            hasStrongSignal = true;
            log.info("{} 强信号组合2触发: 看跌K线形态强 + 价格跌破 + 成交量配合", instId);
        }

        // 强信号组合3: 布林带触顶回落 + RSI超买回落 + ADX中等以上 (适当降低阈值要求)
        if (bollingerStrength > 0.6 && rsiStrength > SHORT_SIGNAL_THRESHOLD && adxStrength > 0.4) {
            hasStrongSignal = true;
            log.info("{} 强信号组合3触发: 布林带触顶回落 + RSI超买回落 + ADX中等以上", instId);
        }

        // 强信号组合4: 量价顶背离 + MACD死叉 + 价格跌破 (适当降低阈值要求)
        if (vpResult.getObvDivergenceStrength() > 0.6 && macdStrength > SHORT_SIGNAL_THRESHOLD && priceBreakdownStrength > SHORT_SIGNAL_THRESHOLD) {
            hasStrongSignal = true;
            log.info("{} 强信号组合4触发: 量价顶背离 + MACD死叉 + 价格跌破", instId);
        }

        // 12. 判断是否达到入场阈值
        boolean entrySignal = scoreRatio >= dynamicThreshold;

        // 13. 强信号判定（如果有强信号组合，可以降低总体阈值）
        if (hasStrongSignal && scoreRatio >= dynamicThreshold * 0.85) {
            entrySignal = true;
            log.info("{} 强信号组合触发，降低入场阈值: {}", instId, String.format("%.1f", dynamicThreshold * 0.85));
        }

        // 14. 新增：如果评分接近阈值且有强核心指标，也可以入场
        if (!entrySignal && scoreRatio >= dynamicThreshold * 0.9) {
            // 检查是否有任一核心指标表现良好
            if (adxStrength > 0.5 || macdStrength > 0.7 || rsiStrength > 0.7 || priceBreakdownStrength > 0.7) {
                entrySignal = true;
                log.info("{} 评分接近阈值且有强核心指标，允许入场: 评分={}, 阈值={}",
                    instId, String.format("%.1f", scoreRatio), String.format("%.1f", dynamicThreshold * 0.9));
            }
        }

        // 15. 额外安全检查 - 即使评分达标，如果ADX过低且没有强信号，也不入场（放在最后确保优先级）
        if (entrySignal && adxStrength < 0.25 && !hasStrongSignal) {
            entrySignal = false;
            log.info("{} 评分达标但ADX过低且无强信号组合，取消入场", instId);
        }

        // 16. 记录决策结果
        log.info("{} 做空入场信号: {}, 评分={}/10, 阈值={}, 强信号={}",
            instId, entrySignal ? "触发" : "未触发",
            String.format("%.1f", scoreRatio),
            String.format("%.1f", dynamicThreshold),
            hasStrongSignal ? "是" : "否");

        return entrySignal;
    }

    /**
     * 检查ADX指标强度（做多方向）
     * 高ADX值表示趋势强，低ADX值表示无趋势或趋势弱
     */
    private double checkADXStrength(List<KLineEntity> klineList) {
        if (klineList.size() < ADX_PERIOD + 5) {
            return 0.0;
        }

        double adx = calculateADX(klineList, 0, ADX_PERIOD);

        // 检查+DI和-DI以确定趋势方向
        double[] diValues = calculateDI(klineList, 0, ADX_PERIOD);
        double plusDI = diValues[0];
        double minusDI = diValues[1];

        // 对于做多，我们希望+DI > -DI
        boolean correctDirection = plusDI > minusDI;

        // 计算ADX强度
        double adxStrength = 0.0;

        // ADX > 25表示强趋势
        if (adx > ADX_STRONG_TREND_THRESHOLD) {
            // 如果方向正确，给予高分
            if (correctDirection) {
                adxStrength = Math.min((adx - ADX_STRONG_TREND_THRESHOLD) / 25.0 + 0.5, 1.0);
            }
            // 如果方向错误但趋势强，给予低分
            else {
                adxStrength = Math.max(0.0, Math.min((adx - ADX_STRONG_TREND_THRESHOLD) / 50.0, 0.3));
            }
        }
        // ADX < 25表示弱趋势或无趋势
        else {
            // 弱趋势情况下，方向正确也有一定分数
            if (correctDirection) {
                adxStrength = Math.max(0.0, adx / ADX_STRONG_TREND_THRESHOLD * 0.4);
            }
        }

        return adxStrength;
    }

    /**
     * 检查ADX指标强度（做空方向）
     * 高ADX值表示趋势强，低ADX值表示无趋势或趋势弱
     */
    private double checkADXStrengthForShort(List<KLineEntity> klineList) {
        if (klineList.size() < ADX_PERIOD + 5) {
            return 0.0;
        }

        double adx = calculateADX(klineList, 0, ADX_PERIOD);

        // 检查+DI和-DI以确定趋势方向
        double[] diValues = calculateDI(klineList, 0, ADX_PERIOD);
        double plusDI = diValues[0];
        double minusDI = diValues[1];

        // 对于做空，我们希望-DI > +DI
        boolean correctDirection = minusDI > plusDI;

        // 计算ADX强度
        double adxStrength = 0.0;

        // ADX > 25表示强趋势
        if (adx > ADX_STRONG_TREND_THRESHOLD) {
            // 如果方向正确，给予高分
            if (correctDirection) {
                adxStrength = Math.min((adx - ADX_STRONG_TREND_THRESHOLD) / 25.0 + 0.5, 1.0);
            }
            // 如果方向错误但趋势强，给予低分
            else {
                adxStrength = Math.max(0.0, Math.min((adx - ADX_STRONG_TREND_THRESHOLD) / 50.0, 0.3));
            }
        }
        // ADX < 25表示弱趋势或无趋势
        else {
            // 弱趋势情况下，方向正确也有一定分数
            if (correctDirection) {
                adxStrength = Math.max(0.0, adx / ADX_STRONG_TREND_THRESHOLD * 0.4);
            }
        }

        return adxStrength;
    }

    /**
     * 检查RSI超买回落强度
     */
    private double checkRsiOverboughtStrength(List<KLineEntity> klineList) {
        if (klineList.size() < RSI_PERIOD + 3) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算RSI值
        double currentRsi = calculateRSI(closePrices, 0, RSI_PERIOD);
        double previousRsi = calculateRSI(closePrices, 1, RSI_PERIOD);
        double prevPreviousRsi = calculateRSI(closePrices, 2, RSI_PERIOD);

        // 检查是否有过超买
        double highestRsi = Math.max(Math.max(currentRsi, previousRsi), prevPreviousRsi);
        boolean wasOverbought = highestRsi > RSI_OVERBOUGHT;

        // 检查RSI下降趋势
        boolean isFalling = currentRsi < previousRsi && previousRsi < prevPreviousRsi;

        if (wasOverbought && currentRsi < previousRsi) {
            // 计算从超买区回落的幅度 - 修复：添加除零保护
            double fallFromOverbought = 0.0;
            if (highestRsi > RSI_OVERBOUGHT) {
                double denominator = highestRsi - 50.0;
                if (denominator > 5.0) { // 确保分母足够大，避免异常结果
                    fallFromOverbought = (highestRsi - currentRsi) / denominator;
                    fallFromOverbought = Math.min(fallFromOverbought, 1.0);
                } else {
                    // 当分母过小时，使用简化计算
                    fallFromOverbought = Math.min((highestRsi - currentRsi) / 20.0, 1.0);
                }
            }

            // 下降速度（前一个RSI与当前的差值）
            double fallSpeed = (previousRsi - currentRsi) / 10.0; // 归一化，假设10点为满分
            fallSpeed = Math.min(fallSpeed, 1.0);

            // 趋势确认（连续下降）
            double trendScore = isFalling ? 0.3 : 0.0;

            // 综合评分
            return Math.min(fallFromOverbought * 0.5 + fallSpeed * 0.2 + trendScore, 1.0);
        }

        return 0.0;
    }

    /**
     * 处理做空入场操作
     */
    private void processShortEntry(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        try {
            log.info("准备做空入场: instId={}, 当前价格={}", instId, currentPrice);

            // 计算止盈止损价格 - 使用做空专用的计算方法
            ShortStopLevelDTO stopLevels = calculateShortStopLevels(instId, currentPrice, klineList);
            if (stopLevels == null) {
                return;
            }

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());
            log.info("做空入场参数: 交易对={}, 价格={}, 数量={}, 止损价={}, 止盈价1={}, 止盈价2={}",
                instId, currentPrice, sz, stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2);

            // 执行开仓
            trade(instId, "sell", sz, "short", false);
            ThreadUtil.sleep(100);

            // 设置止盈止损
            setupShortStopOrders(instId, currentPrice, stopLevels);

        } catch (Exception e) {
            log.error("执行做空入场操作异常", e);
        }
    }

    /**
     * 计算做空止盈止损价格
     */
    private ShortStopLevelDTO calculateShortStopLevels(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        // 计算ATR用于设置止损和止盈
        double atr = calculateATR(klineList, 0, ATR_PERIOD);
        if (atr <= 0) {
            log.info("ATR计算错误，取消做空入场");
            return null;
        }

        // 设置止损价 (做空止损在价格上方，使用更保守的倍数)
        BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() + atr * SHORT_STOP_LOSS_ATR_MULTIPLIER)
            .setScale(currentPrice.scale(), RoundingMode.UP);

        // 计算止盈价格（基于ATR倍数，做空止盈在价格下方）
        BigDecimal takeProfitPrice1 = currentPrice.subtract(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_1))
            .setScale(currentPrice.scale(), RoundingMode.UP);
        BigDecimal takeProfitPrice2 = currentPrice.subtract(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_2))
            .setScale(currentPrice.scale(), RoundingMode.UP);

        return new ShortStopLevelDTO(stopLossPrice, takeProfitPrice1, takeProfitPrice2);
    }

    /**
     * 设置做空止盈止损订单
     */
    private void setupShortStopOrders(String instId, BigDecimal currentPrice, ShortStopLevelDTO stopLevels) {
        // 获取持仓信息
        Position pos = getPos(instId);
        if (pos == null) {
            log.info("未获取到做空持仓信息，无法设置止盈止损");
            return;
        }

        log.info("做空开仓成功: 交易对={}, 持仓ID={}, 数量={}", instId, pos.getPosId(), pos.getAvailPos());

        // 计算各止盈档位的仓位大小
        BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
        BigDecimal tp1Size = totalPos.multiply(BigDecimal.valueOf(0.5)) // 第一档平仓50%
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal tp2Size = totalPos.subtract(tp1Size) // 第二档平仓剩余50%
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 设置止损单
        String stopLossPx = stopLevels.stopLossPrice.toString();
        algoTradeLoss(instId, "buy", totalPos.toString(), "short", stopLossPx);
        log.info("做空止损单设置成功: 交易对={}, 价格={}, 数量={}", instId, stopLossPx, totalPos);

        // 设置止盈单
        String tp1Px = stopLevels.takeProfitPrice1.toString();
        String tp2Px = stopLevels.takeProfitPrice2.toString();

        algoTradeWin(instId, "buy", getMaxSellableSz(instId, tp1Size.doubleValue()), "short", tp1Px);
        log.info("做空止盈单1设置成功: 交易对={}, 价格={}, 数量={}", instId, tp1Px, tp1Size);

        algoTradeWin(instId, "buy", getMaxSellableSz(instId, tp2Size.doubleValue()), "short", tp2Px);
        log.info("做空止盈单2设置成功: 交易对={}, 价格={}, 数量={}", instId, tp2Px, tp2Size);

        // 发送消息通知
        messageService.send(instId + "空单策略", MessageFormat.format("{0}空头入场: 入场价={1}, 止损价={2}, 止盈1={3}, 止盈2={4}",
            instId, new BigDecimal(pos.getAvgPx()).setScale(currentPrice.scale(), RoundingMode.DOWN), stopLevels.stopLossPrice, stopLevels.takeProfitPrice1,
            stopLevels.takeProfitPrice2
        ));

        // 记录交易信息到Redis
        String key = SHORT_STRATEGY_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(12));
    }

    /**
     * 用于存储做空止盈止损价格的内部类
     */
    private static class ShortStopLevelDTO {
        private final BigDecimal stopLossPrice;
        private final BigDecimal takeProfitPrice1;
        private final BigDecimal takeProfitPrice2;

        public ShortStopLevelDTO(BigDecimal stopLossPrice, BigDecimal takeProfitPrice1, BigDecimal takeProfitPrice2) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice1 = takeProfitPrice1;
            this.takeProfitPrice2 = takeProfitPrice2;
        }
    }

    /**
     * 检查看跌K线形态强度 (0-1)
     */
    private double checkBearishCandlePatternStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return 0.0;
        }

        // 检查各种K线形态的强度，返回最强的一个
        double shootingStarStrength = checkShootingStarPatternStrength(klineList.get(0));
        double eveningStarStrength = checkEveningStarStrength(klineList);
        double bearishEngulfingStrength = checkBearishEngulfingStrength(klineList);
        double darkCloudCoverStrength = checkDarkCloudCoverStrength(klineList);

        // 返回最强的形态信号强度
        return Math.max(Math.max(shootingStarStrength, eveningStarStrength),
            Math.max(bearishEngulfingStrength, darkCloudCoverStrength));
    }

    /**
     * 检查流星线形态强度 (0-1)
     * 流星线是顶部反转信号，上影线长，实体小，下影线短或没有
     */
    private double checkShootingStarPatternStrength(KLineEntity kline) {
        double body = Math.abs(kline.getClose().doubleValue() - kline.getOpen().doubleValue());
        double upperShadow = kline.getHigh().doubleValue() - Math.max(kline.getOpen().doubleValue(), kline.getClose().doubleValue());
        double lowerShadow = Math.min(kline.getOpen().doubleValue(), kline.getClose().doubleValue()) - kline.getLow().doubleValue();
        double totalRange = kline.getHigh().doubleValue() - kline.getLow().doubleValue();

        if (totalRange == 0) return 0.0;

        // 流星线特征：上影线长度至少是实体的两倍，下影线很短 - 修复：添加body除零保护
        if (body > 0 && upperShadow > body * 2 && lowerShadow < body * 0.5) {
            // 计算形态质量（0-1）
            double shadowBodyRatio = upperShadow / body;
            double lowerShadowRatio = lowerShadow / totalRange;

            // 上影线比例得分(最高0.7)
            double shadowScore = Math.min((shadowBodyRatio - 2.0) / 5.0, 0.7);

            // 下影线比例得分(最高0.3)
            double lowerShadowScore = 0.3 * (1.0 - Math.min(lowerShadowRatio * 5, 1.0));

            // 如果是阴线加分
            double bearishBonus = kline.getClose().doubleValue() < kline.getOpen().doubleValue() ? 0.2 : 0.0;

            return Math.min(shadowScore + lowerShadowScore + bearishBonus, 1.0);
        }

        return 0.0;
    }

    /**
     * 检查黄昏之星形态强度 (0-1)
     * 黄昏之星是顶部反转信号，三根K线组成
     */
    private double checkEveningStarStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 3) {
            return 0.0;
        }

        KLineEntity first = klineList.get(2);
        KLineEntity second = klineList.get(1);
        KLineEntity third = klineList.get(0);

        // 基本条件检查
        boolean firstIsBullish = first.getClose().doubleValue() > first.getOpen().doubleValue();
        double firstBodySize = Math.abs(first.getOpen().doubleValue() - first.getClose().doubleValue());
        double secondBodySize = Math.abs(second.getOpen().doubleValue() - second.getClose().doubleValue());
        boolean secondIsSmall = secondBodySize < firstBodySize * 0.5;
        boolean thirdIsBearish = third.getClose().doubleValue() < third.getOpen().doubleValue();
        double thirdBodySize = Math.abs(third.getOpen().doubleValue() - third.getClose().doubleValue());

        if (firstIsBullish && secondIsSmall && thirdIsBearish) {
            // 计算第三根K线相对于第一根K线的回落程度(0-1)
            double retracementRatio = 0.0;
            if (firstBodySize > 0) {
                double retracementAmount = first.getClose().doubleValue() - third.getClose().doubleValue();
                retracementRatio = Math.min(retracementAmount / firstBodySize, 1.0);
                // 回落超过第一根K线实体50%加分
                retracementRatio = retracementRatio > 0.5 ? (0.5 + (retracementRatio - 0.5) * 2) : retracementRatio;
            }

            // 第二根K线越小，形态越明显
            double secondK_quality = Math.max(0.0, 1.0 - (secondBodySize / firstBodySize) * 2);

            // 第三根K线实体越大，信号越强
            double thirdK_strength = Math.min(thirdBodySize / firstBodySize, 1.0);

            // 综合评分
            return Math.min((retracementRatio * 0.5) + (secondK_quality * 0.3) + (thirdK_strength * 0.2), 1.0);
        }

        return 0.0;
    }

    /**
     * 检查看跌吞没形态强度 (0-1)
     */
    private double checkBearishEngulfingStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 2) {
            return 0.0;
        }

        KLineEntity previous = klineList.get(1);
        KLineEntity current = klineList.get(0);

        // 基本条件检查
        boolean previousIsBullish = previous.getClose().doubleValue() > previous.getOpen().doubleValue();
        boolean currentIsBearish = current.getClose().doubleValue() < current.getOpen().doubleValue();

        if (previousIsBullish && currentIsBearish) {
            double prevBody = Math.abs(previous.getOpen().doubleValue() - previous.getClose().doubleValue());
            double currBody = Math.abs(current.getOpen().doubleValue() - current.getClose().doubleValue());

            // 检查吞没程度 - 修复：看跌吞没应该是当前阴线完全包含前一根阳线
            boolean isEngulfing = current.getOpen().doubleValue() > previous.getClose().doubleValue() &&
                current.getClose().doubleValue() < previous.getOpen().doubleValue();

            if (isEngulfing) {
                // 吞没比例 (当前实体相对前一根的大小)
                double engulfingRatio = Math.min(currBody / prevBody, 2.0) / 2.0;

                // 额外吞没范围（如果不仅吞没实体还吞没了影线）
                double lowerExcess = Math.max(0.0, current.getClose().doubleValue() - previous.getLow().doubleValue());
                double upperExcess = Math.max(0.0, previous.getHigh().doubleValue() - current.getOpen().doubleValue());
                double totalRange = previous.getHigh().doubleValue() - previous.getLow().doubleValue();
                double excessScore = totalRange > 0 ? Math.min((lowerExcess + upperExcess) / totalRange, 0.5) : 0;

                // 综合评分
                return Math.min(0.5 + (engulfingRatio * 0.35) + excessScore, 1.0);
            }
        }

        return 0.0;
    }

    /**
     * 检查乌云盖顶形态强度 (0-1)
     */
    private double checkDarkCloudCoverStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 2) {
            return 0.0;
        }

        KLineEntity previous = klineList.get(1);
        KLineEntity current = klineList.get(0);

        // 基本条件检查
        boolean previousIsBullish = previous.getClose().doubleValue() > previous.getOpen().doubleValue();
        boolean currentIsBearish = current.getClose().doubleValue() < current.getOpen().doubleValue();

        if (previousIsBullish && currentIsBearish) {
            // 检查开盘价是否高于前一根K线的高点
            boolean openAbovePrevHigh = current.getOpen().doubleValue() > previous.getHigh().doubleValue();

            // 检查收盘价是否深入前一根K线实体
            double prevBody = previous.getClose().doubleValue() - previous.getOpen().doubleValue();
            double penetration = (previous.getClose().doubleValue() - current.getClose().doubleValue()) / prevBody;
            boolean deepPenetration = penetration > 0.5;

            if (openAbovePrevHigh || current.getOpen().doubleValue() > previous.getClose().doubleValue()) {
                // 计算形态强度
                double penetrationScore = Math.min(penetration, 1.0);
                double openGapScore = openAbovePrevHigh ? 0.3 : 0.0;

                // 当前K线实体大小评分
                double currBody = Math.abs(current.getOpen().doubleValue() - current.getClose().doubleValue());
                double bodySizeRatio = Math.min(currBody / prevBody, 2.0) / 2.0;

                // 综合评分
                return Math.min(penetrationScore * 0.5 + openGapScore + bodySizeRatio * 0.2, 1.0);
            }
        }

        return 0.0;
    }

    /**
     * 检查MACD做空信号强度 (0-1)
     */
    private double checkMacdShortStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 50) {
            return 0.0;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算当前和前一个周期的MACD值 - 修复：正确的前一周期数据
        double[] macd = calculateMACD(closePrices);
        double[] prevMacd = calculateMACD(closePrices.subList(0, closePrices.size() - 1));

        double macdLine = macd[0];
        double signalLine = macd[1];
        double histogram = macd[2];
        double prevHistogram = prevMacd[2];

        // 指标强度评分
        double strength = 0.0;

        // 检查死叉信号
        if (macdLine < signalLine && prevMacd[0] >= prevMacd[1]) {
            // 死叉信号强度（取决于交叉角度）
            double crossAngle = (prevMacd[0] - prevMacd[1]) - (macdLine - signalLine);
            double crossStrength = Math.min(crossAngle / 0.001, 1.0); // 归一化，假设0.001为基准变化率
            strength = Math.max(strength, 0.6 + crossStrength * 0.4);
        }

        // 检查柱状图转负
        if (histogram < 0 && prevHistogram >= 0) {
            // 柱状图由正转负的强度
            double histogramStrength = Math.min(Math.abs(histogram) / 0.001, 1.0); // 归一化
            strength = Math.max(strength, 0.5 + histogramStrength * 0.3);
        }

        // 检查顶部背离
        boolean divergence = checkMacdTopDivergence(klineList, closePrices);
        if (divergence) {
            // 背离信号通常很强
            strength = Math.max(strength, 0.8);
        }

        return strength;
    }

    /**
     * 检查MACD顶部背离
     * 当价格创新高但MACD不创新高时，可能出现顶部背离
     */
    private boolean checkMacdTopDivergence(List<KLineEntity> klineList, List<Double> closePrices) {
        if (klineList.size() < 20) {
            return false;
        }

        // 寻找最近的两个高点
        int high1Index = -1;
        int high2Index = -1;

        for (int i = 5; i < klineList.size() - 5; i++) {
            // 判断是否为高点 (简单判断：前后各两根K线的价格都比当前价格低)
            if (klineList.get(i - 2).getHigh().doubleValue() < klineList.get(i).getHigh().doubleValue() &&
                klineList.get(i - 1).getHigh().doubleValue() < klineList.get(i).getHigh().doubleValue() &&
                klineList.get(i + 1).getHigh().doubleValue() < klineList.get(i).getHigh().doubleValue() &&
                klineList.get(i + 2).getHigh().doubleValue() < klineList.get(i).getHigh().doubleValue()) {

                if (high1Index == -1) {
                    high1Index = i;
                } else {
                    high2Index = high1Index;
                    high1Index = i;
                    break;
                }
            }
        }

        if (high1Index != -1 && high2Index != -1) {
            // 检查价格是否创新高
            boolean priceNewHigh = klineList.get(high1Index).getHigh().doubleValue() >
                klineList.get(high2Index).getHigh().doubleValue();

            if (priceNewHigh) {
                // 检查MACD是否创新高
                double macdHigh1 = calculateMACD(closePrices.subList(0, closePrices.size() - high1Index))[0];
                double macdHigh2 = calculateMACD(closePrices.subList(0, closePrices.size() - high2Index))[0];

                // 如果价格创新高但MACD不创新高，则出现顶部背离
                return macdHigh1 < macdHigh2;
            }
        }

        return false;
    }

    /**
     * 检查价格跌破强度 (0-1)
     */
    private double checkPriceBreakdownStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 30) {
            return 0.0;
        }

        // 计算10周期简单移动平均线
        List<Double> sma10 = calculateSMA(klineList, 10);
        if (sma10.size() < 5) return 0.0;

        // 当前价格和SMA值
        double currentPrice = klineList.get(0).getClose().doubleValue();
        double smaValue = sma10.get(0);

        // 计算SMA斜率
        double smaSlope = sma10.get(3) - sma10.get(0);

        // 跌破强度评分
        double strength = 0.0;

        if (currentPrice < smaValue) {
            // 价格跌破幅度
            double breakdownPercentage = (smaValue - currentPrice) / smaValue * 100;
            double breakdownScore = Math.min(breakdownPercentage, 1.0); // 1%跌破为满分

            // SMA斜率评分（平缓或向下更好）
            double slopeScore = 0.0;
            if (smaSlope <= 0) {
                // 向下倾斜，满分
                slopeScore = 1.0;
            } else {
                // 上升趋势中减缓，部分得分
                slopeScore = Math.max(0.0, 1.0 - smaSlope * 1000); // 归一化，视上升速度而定
            }

            // K线实体评分（阴线且收盘较低更好）
            KLineEntity currentK = klineList.get(0);
            double bodyRatio = 0.0;
            if (currentK.getClose().doubleValue() < currentK.getOpen().doubleValue()) {
                double range = currentK.getHigh().doubleValue() - currentK.getLow().doubleValue();
                if (range > 0) {
                    bodyRatio = (currentK.getOpen().doubleValue() - currentK.getClose().doubleValue()) / range;
                }
            }
            double bodyScore = Math.min(bodyRatio * 2, 1.0);

            // 综合评分
            strength = breakdownScore * 0.5 + slopeScore * 0.3 + bodyScore * 0.2;
        }

        return strength;
    }

    /**
     * 检查布林带做空信号强度 (0-1)
     */
    private double checkBollingerBandShortStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 30) {
            return 0.0;
        }

        int period = 20;
        double devFactor = 2.0;

        // 计算20周期SMA
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        List<Double> sma = new java.util.ArrayList<>();
        for (int i = 0; i < closePrices.size() - period + 1; i++) {
            double sum = 0;
            for (int j = i; j < i + period; j++) {
                sum += closePrices.get(j);
            }
            sma.add(sum / period);
        }

        // 计算标准差
        double sum = 0;
        for (int i = 0; i < period; i++) {
            sum += Math.pow(closePrices.get(i) - sma.get(0), 2);
        }
        double stdDev = Math.sqrt(sum / period);

        // 计算布林带上轨
        double upperBand = sma.get(0) + (stdDev * devFactor);

        // 信号强度评分
        double strength = 0.0;

        // 检查触及上轨情况
        boolean touchedUpperBand = false;
        int touchIndex = -1;
        for (int i = 1; i < 5 && i < klineList.size(); i++) {
            if (klineList.get(i).getHigh().doubleValue() >= upperBand) {
                touchedUpperBand = true;
                touchIndex = i;
                break;
            }
        }

        if (touchedUpperBand && touchIndex > 0) {
            // 当前K线价格与上轨的距离
            double currentPrice = klineList.get(0).getClose().doubleValue();
            double distanceFromUpper = upperBand - currentPrice;
            double bandWidth = upperBand - (sma.get(0) - (stdDev * devFactor));

            // 回落幅度评分 (从上轨到中轨之间)
            double fallRatio = bandWidth > 0 ? distanceFromUpper / (bandWidth / 2) : 0;
            double fallScore = Math.min(fallRatio, 1.0);

            // 回落速度（与触及上轨K线的距离越近回落越迅速）
            double fallSpeed = 1.0 / (double) touchIndex;

            // 回落K线形态（阴线且实体大占比高）
            KLineEntity currentK = klineList.get(0);
            boolean isCurrentBearish = currentK.getClose().doubleValue() < currentK.getOpen().doubleValue();
            double bodySize = Math.abs(currentK.getClose().doubleValue() - currentK.getOpen().doubleValue());
            double range = currentK.getHigh().doubleValue() - currentK.getLow().doubleValue();
            double bodySizeRatio = range > 0 ? bodySize / range : 0;

            // K线形态得分
            double candleScore = isCurrentBearish ? bodySizeRatio : 0;

            // 布林带收缩程度（窄带扩张信号更强） - 修复：添加边界检查
            double prevBandWidth = 0;
            if (sma.size() > 5) {
                // 计算5个周期前的标准差
                double prevSum = 0;
                int count = 0;
                for (int i = 5; i < 5 + period && i < closePrices.size() && i < sma.size(); i++) {
                    prevSum += Math.pow(closePrices.get(i) - sma.get(5), 2);
                    count++;
                }
                if (count > 0) {
                    double prevStdDev = Math.sqrt(prevSum / count);
                    prevBandWidth = prevStdDev * 2 * devFactor;
                }
            }

            // 带宽收缩评分
            double bandwidthScore = 0.0;
            if (prevBandWidth > 0) {
                double bandwidthRatio = (stdDev * 2 * devFactor) / prevBandWidth;
                bandwidthScore = bandwidthRatio < 1.0 ? (1.0 - bandwidthRatio) : 0.0;
            }

            // 综合评分
            strength = fallScore * 0.4 + fallSpeed * 0.2 + candleScore * 0.2 + bandwidthScore * 0.2;
        }

        return strength;
    }

    /**
     * 检查做空成交量强度 (0-1)
     */
    private double checkShortVolumeStrength(List<KLineEntity> klineList) {
        if (klineList.size() < 10) {
            return 0.0;
        }

        KLineEntity current = klineList.get(0);

        // 计算前9根K线的平均成交量
        double avgVolume = 0.0;
        for (int i = 1; i < 10; i++) {
            avgVolume += klineList.get(i).getVolume();
        }
        avgVolume /= 9.0;

        // 当前K线是否为阴线
        boolean currentIsBearish = current.getClose().doubleValue() < current.getOpen().doubleValue();

        if (currentIsBearish && avgVolume > 0) {
            // 计算成交量放大比例
            double volumeRatio = current.getVolume() / avgVolume;

            // 以1.5倍为基准，3倍以上为满分
            double volumeScore = 0.0;
            if (volumeRatio >= 1.5) {
                volumeScore = Math.min((volumeRatio - 1.5) / 1.5, 1.0);
            }

            // 价格实体大小影响
            double bodySize = Math.abs(current.getClose().doubleValue() - current.getOpen().doubleValue());
            double range = current.getHigh().doubleValue() - current.getLow().doubleValue();
            double bodySizeRatio = range > 0 ? bodySize / range : 0;
            double bodyScore = Math.min(bodySizeRatio * 2, 1.0);

            // 综合评分
            return Math.min(volumeScore * 0.7 + bodyScore * 0.3, 1.0);
        }

        return 0.0;
    }

    /**
     * 分析做空量价关系
     * 增加对顶部成交量萎缩后放大的特征识别
     */
    private VolumePriceAnalysisResult analyzeVolumePriceForShort(List<KLineEntity> kline5m) {
        double volumeBreakoutStrength = 0.0;
        double volumeShrinkStrength = 0.0;
        double obvTrendStrength = 0.0;
        double obvBreakoutStrength = 0.0;
        double obvDivergenceStrength = 0.0;
        StringBuilder volumeLog = new StringBuilder();

        if (kline5m.size() < VOLUME_ANALYSIS_MIN_BARS) {
            return new VolumePriceAnalysisResult(
                volumeBreakoutStrength, volumeShrinkStrength, obvTrendStrength,
                obvBreakoutStrength, obvDivergenceStrength, volumeLog.toString()
            );
        }

        // 1. 成交量形态识别
        double currVolume = kline5m.get(0).getVolume();
        double avgVolume10 = calculateAvgVolume(kline5m, 1, 11);
        double avgVolume20 = calculateAvgVolume(kline5m, 1, 21);

        // 量能突破（做空信号：阴线放量）
        KLineEntity current = kline5m.get(0);
        boolean isBearish = current.getClose().doubleValue() < current.getOpen().doubleValue();

        if (isBearish && currVolume > avgVolume10 * VOLUME_BREAKOUT_THRESHOLD && currVolume > avgVolume20 * 1.5) {
            volumeBreakoutStrength = 1.0;
            volumeLog.append("阴线量能突破 ");
        } else if (isBearish && currVolume > avgVolume10 * VOLUME_INCREASE_THRESHOLD) {
            volumeBreakoutStrength = 0.6;
            volumeLog.append("阴线量能放大 ");
        }

        // 量能萎缩（做空信号：上涨时量能萎缩）
        boolean isBullish = current.getClose().doubleValue() > current.getOpen().doubleValue();
        if (isBullish && currVolume < avgVolume10 * VOLUME_SHRINK_THRESHOLD && currVolume < avgVolume20 * VOLUME_SHRINK_THRESHOLD) {
            volumeShrinkStrength = 0.7;
            volumeLog.append("阳线量能萎缩 ");
        }

        // 新增：检测顶部成交量萎缩后放大的特征
        boolean volumeShrinkThenExpand = checkVolumeShrinkThenExpand(kline5m);
        if (volumeShrinkThenExpand) {
            volumeBreakoutStrength = Math.max(volumeBreakoutStrength, 0.8);
            volumeLog.append("顶部量能萎缩后放大 ");
        }

        // 2. OBV指标分析
        List<Double> closeList = extractClosePrices(kline5m);
        List<Double> volumeList = extractVolumes(kline5m);
        List<Double> obvList = calculateOBV(closeList, volumeList);

        if (obvList.size() >= OBV_TREND_BARS) {
            // OBV趋势（近10根单调性）
            int obvTrend = isMonotonic(obvList.subList(0, OBV_TREND_BARS));
            if (obvTrend == -1) {
                obvTrendStrength = 0.8;
                volumeLog.append("OBV下降 ");
            } else if (obvTrend == 1) {
                obvTrendStrength = 0.0;
                volumeLog.append("OBV上升 ");
            } else {
                obvTrendStrength = 0.3;
            }

            // OBV突破（新高/新低）
            double obvNow = obvList.get(0);
            double obvMax10 = getMaxValue(obvList.subList(1, 11));
            double obvMin10 = getMinValue(obvList.subList(1, 11));

            if (obvNow < obvMin10) {
                obvBreakoutStrength = 1.0;
                volumeLog.append("OBV新低 ");
            } else if (obvNow > obvMax10) {
                obvBreakoutStrength = 0.0;
                volumeLog.append("OBV新高 ");
            } else {
                obvBreakoutStrength = 0.4;
            }

            // OBV背离（价格创新高但OBV未创新高 - 做空信号）
            double priceMax10 = getMaxValue(closeList.subList(1, 11));
            if (closeList.get(0) > priceMax10 && obvNow < obvMax10) {
                obvDivergenceStrength = 1.0;
                volumeLog.append("量价顶背离 ");
            }
        }

        return new VolumePriceAnalysisResult(
            volumeBreakoutStrength, volumeShrinkStrength, obvTrendStrength,
            obvBreakoutStrength, obvDivergenceStrength, volumeLog.toString()
        );
    }

    /**
     * 检测顶部成交量萎缩后放大的特征
     * 这是市场顶部的典型特征：先量能萎缩，价格继续上涨，然后突然放量下跌
     */
    private boolean checkVolumeShrinkThenExpand(List<KLineEntity> klineList) {
        if (klineList.size() < 10) return false;

        // 当前K线
        KLineEntity current = klineList.get(0);
        boolean currentIsBearish = current.getClose().doubleValue() < current.getOpen().doubleValue();

        // 如果当前不是阴线，不符合条件
        if (!currentIsBearish) return false;

        // 计算前3-8根K线的平均成交量（检查之前是否有成交量萎缩）
        double avgVolumePrevious = 0;
        for (int i = 3; i < 9 && i < klineList.size(); i++) {
            avgVolumePrevious += klineList.get(i).getVolume();
        }
        avgVolumePrevious /= 6;

        // 计算前1-2根K线的平均成交量（检查最近是否有成交量放大）
        double avgVolumeRecent = (klineList.get(1).getVolume() + klineList.get(2).getVolume()) / 2;

        // 检查之前的成交量是否萎缩
        boolean previousVolumeShrink = avgVolumeRecent < avgVolumePrevious * 0.8;

        // 检查当前成交量是否放大
        boolean currentVolumeExpand = current.getVolume() > avgVolumeRecent * 1.5;

        // 检查价格是否在高位
        double highestPrice = 0;
        for (int i = 1; i < 10 && i < klineList.size(); i++) {
            highestPrice = Math.max(highestPrice, klineList.get(i).getHigh().doubleValue());
        }

        boolean priceNearHigh = current.getOpen().doubleValue() > highestPrice * 0.98;

        // 满足条件：之前成交量萎缩，当前成交量放大，且价格在高位
        return previousVolumeShrink && currentVolumeExpand && priceNearHigh;
    }

}
