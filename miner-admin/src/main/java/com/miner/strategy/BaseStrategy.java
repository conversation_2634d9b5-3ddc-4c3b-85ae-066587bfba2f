package com.miner.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.common.utils.redis.RedisUtils;
import com.miner.system.indicator.DataHelper;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.okx.bean.Position;
import com.miner.system.okx.bean.ticket.Bar;
import com.miner.system.okx.bean.trade.param.PlaceAlgoOrder;
import com.miner.system.okx.bean.trade.param.PlaceOrder;
import com.miner.system.okx.service.account.AccountAPIService;
import com.miner.system.okx.service.account.impl.AccountAPIServiceImpl;
import com.miner.system.okx.service.marketData.MarketDataAPIService;
import com.miner.system.okx.service.marketData.impl.MarketDataAPIServiceImpl;
import com.miner.system.okx.service.message.MessageService;
import com.miner.system.okx.service.publicData.PublicDataAPIService;
import com.miner.system.okx.service.publicData.impl.PublicDataAPIServiceImpl;
import com.miner.system.okx.service.trade.TradeAPIService;
import com.miner.system.okx.service.trade.impl.TradeAPIServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.fitting.PolynomialCurveFitter;
import org.apache.commons.math3.fitting.WeightedObservedPoints;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseStrategy {
    PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();
    MarketDataAPIService marketDataAPIService = new MarketDataAPIServiceImpl();
    TradeAPIService tradeAPIService = new TradeAPIServiceImpl();
    AccountAPIService accountAPIService = new AccountAPIServiceImpl();
    @Resource
    public MessageService messageService;

    public List<String> btcEth = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");

    public static List<Double> calculateMAOBV(List<Double> obvValues, int period) {
        List<Double> maobvValues = new ArrayList<>();

        for (int i = 0; i < obvValues.size(); i++) {
            if (i < period - 1) {
                maobvValues.add(null); // 前几天的数据无法计算MAOBV，填充null
            } else {
                double sum = 0.0;
                for (int j = i - period + 1; j <= i; j++) {
                    sum += obvValues.get(j);
                }
                maobvValues.add(sum / period);
            }
        }

        return maobvValues;
    }

    public abstract void run();

    /**
     * 检查给定的列表是否严格单调递增或递减。
     *
     * @param list 要检查的列表。
     * @return 如果列表严格单调递增1  递减-1  否则返回0，
     */
    public int isMonotonic(List<Double> list) {
        if (list == null || list.size() < 2) {
            return 0;
        }
        boolean up = true, down = true;
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i) < list.get(i - 1)) {
                up = false;
            }
            if (list.get(i) > list.get(i - 1)) {
                down = false;
            }
        }
        if (up && !down) {
            return 1;    // 单调递增（允许持平）
        }
        if (!up && down) {
            return -1;   // 单调递减（允许持平）
        }
        return 0;                     // 非单调
    }


    public List<Position> getPosList() {
        JSONObject positions = accountAPIService.getPositions("SWAP", null, null);
        JSONArray data = positions.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        }
        List<Position> position = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obg = data.getJSONObject(i);
            position.add(obg.toJavaObject(Position.class));
        }
        return position;
    }

    public Position getPos(String instId) {
        JSONObject positions = accountAPIService.getPositions("SWAP", instId, null);
        JSONArray data = positions.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            return null;
        }
        List<Position> position = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obg = data.getJSONObject(i);
            position.add(obg.toJavaObject(Position.class));
        }
        return position.get(0);
    }

    public synchronized List<String> getInstIds() {
        return getInstIds(20);
    }

    public synchronized List<String> getInstIds(int size) {
        String key = "all-trade-inst-" + size;
        final List<String> cacheList = RedisUtils.getCacheList(key);
        if (CollUtil.isNotEmpty(cacheList)) {
            return cacheList;
        }
        Map<String, Double> volumeMap = new HashMap<>();
        JSONObject instruments = publicDataAPIService.getInstruments("SWAP", null, null);

        final JSONArray jsonArray = instruments.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            final JSONObject object = jsonArray.getJSONObject(i);
            final String instId = object.getString("instId");
            if (!instId.contains("USDT")) {
                continue;
            }
            final double volume = getVolume(instId);
            volumeMap.put(instId, volume);
            ThreadUtil.sleep(100);
        }
        final List<String> ids = volumeMap.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .limit(size).map(Map.Entry::getKey)
            .collect(Collectors.toList());
        RedisUtils.setCacheList(key, ids);
        RedisUtils.expire(key, Duration.ofHours(12));
        return ids;
    }

    public String getPz(String price, double percent) {
        int closeScale = new BigDecimal(String.valueOf(price)).scale();
        return new BigDecimal(price).multiply(BigDecimal.valueOf(percent))
            .setScale(closeScale, RoundingMode.HALF_UP)
            .toString();
    }

    public String getPz(double price, int scale) {
        return new BigDecimal(price)
            .setScale(scale, RoundingMode.HALF_UP)
            .toString();
    }

    /**
     * kdj上穿
     */
    public boolean kdjCrossUp(List<KLineEntity> kLineEntities) {
        return kLineEntities.get(0).getJ() > kLineEntities.get(0).getD()
            && kLineEntities.get(1).getJ() < kLineEntities.get(1).getD();
    }

    /**
     * 近2个周期kdj上穿
     */
    public boolean kdjCrossUp2Cycle(List<KLineEntity> kLineEntities) {
        return (kLineEntities.get(0).getJ() > kLineEntities.get(0).getD()
            && kLineEntities.get(1).getJ() < kLineEntities.get(1).getD())
            ||
            (kLineEntities.get(1).getJ() > kLineEntities.get(1).getD()
                && kLineEntities.get(2).getJ() < kLineEntities.get(2).getD());
    }

    /**
     * 近2个周期macd上穿
     */
    public boolean macdCrossUp2Cycle(List<KLineEntity> kLineEntities) {
        return (kLineEntities.get(0).getMacd() > 0
            && kLineEntities.get(1).getMacd() < 0)
            ||
            (kLineEntities.get(1).getMacd() > 0
                && kLineEntities.get(2).getMacd() < 0);
    }

    /**
     * 近2个周期macd下穿
     */
    public boolean macdCrossDown2Cycle(List<KLineEntity> kLineEntities) {
        return (kLineEntities.get(0).getMacd() < 0
            && kLineEntities.get(1).getMacd() > 0)
            ||
            (kLineEntities.get(1).getMacd() < 0
                && kLineEntities.get(2).getMacd() > 0);
    }

    /**
     * kdj下穿
     */
    public boolean kdjCrossDown(List<KLineEntity> kLineEntities) {
        return kLineEntities.get(0).getJ() < kLineEntities.get(0).getD()
            && kLineEntities.get(1).getJ() > kLineEntities.get(1).getD();
    }

    /**
     * 近2个周期kdj下穿
     */
    public boolean kdjCrossDown2Cycle(List<KLineEntity> kLineEntities) {
        return (kLineEntities.get(0).getJ() < kLineEntities.get(0).getD()
            && kLineEntities.get(1).getJ() > kLineEntities.get(1).getD())
            || (kLineEntities.get(1).getJ() < kLineEntities.get(1).getD()
            && kLineEntities.get(2).getJ() > kLineEntities.get(2).getD());
    }

    public double getRate(String instId) {
        JSONObject dayJson = marketDataAPIService.getCandlesticks(instId, null, null, "1D", "1");
        List<KLineEntity> dayLine = parseKLineList(dayJson, instId);
        return (dayLine.get(0).getClose().doubleValue() - dayLine.get(0).getOpen()
            .doubleValue()) / dayLine.get(0).getOpen().doubleValue() * 100;
    }

    public double getRate(KLineEntity entity) {
        return (entity.getClose().doubleValue() - entity.getOpen()
            .doubleValue()) / entity.getOpen().doubleValue() * 100;
    }

    public double calculateUpperShadow(KLineEntity entity) {
        return calculateUpperShadow(entity.getOpen().doubleValue(), entity.getClose().doubleValue(), entity.getHigh()
            .doubleValue());
    }

    /**
     * 计算K线的上引线长度。
     *
     * @param open  开盘价
     * @param close 收盘价
     * @param high  最高价
     * @return 上引线长度
     */
    public static double calculateUpperShadow(double open, double close, double high) {
        double maxPrice = Math.max(open, close);
        return Math.max(0, high - maxPrice);
    }


    public double calculateUpperShadowPercent(KLineEntity entity) {
        return calculateUpperShadowPercent(entity.getOpen().doubleValue(), entity.getClose()
            .doubleValue(), entity.getHigh()
            .doubleValue());
    }


    /**
     * 计算K线的上引线相对于实体长度的百分比。
     *
     * @param open  开盘价
     * @param close 收盘价
     * @param high  最高价
     * @return 上引线相对于实体长度的百分比
     */
    public double calculateUpperShadowPercent(double open, double close, double high) {
        double bodyLength = Math.abs(close - open); // 实体长度
        double upperShadowLength = Math.max(0, high - Math.max(open, close)); // 上引线长度

        if (bodyLength == 0) {
            return Double.NaN; // 或者返回一个特殊值，如 -1
        }

        return (upperShadowLength / bodyLength) * 100; // 百分比
    }


    public double getVolume(String instId) {
        JSONObject tickers;
        tickers = marketDataAPIService.getTicker(instId);
        JSONObject data = tickers.getJSONArray("data").getJSONObject(0);
        final Double last = data.getDouble("last");
        final Double vol24h = data.getDouble("volCcy24h");
        return last * vol24h;
    }

    public JSONObject getInstrument(String instId) {
        String key = "instrument:" + instId;
        final String cacheObject = RedisUtils.getCacheObject(key);
        if (cacheObject != null) {
            return JSONObject.parseObject(cacheObject);
        }
        PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();
        JSONObject instruments = publicDataAPIService.getInstruments("SWAP", null, instId);
        JSONObject jsonObject = instruments.getJSONArray("data").getJSONObject(0);
        RedisUtils.setCacheObject(key, jsonObject.toJSONString(), Duration.ofDays(1));
        return jsonObject;
    }

    public void parseBarList(JSONObject barJson, List<Bar> barList, String instId) {
        JSONArray dataArray = barJson.getJSONArray("data");
        for (int i = 0; i < dataArray.size(); i++) {
            JSONArray temp = dataArray.getJSONArray(i);
            //    ts	String	开始时间，Unix时间戳的毫秒数格式，如 1597026383085
            //o	String	开盘价格
            //h	String	最高价格
            //l	String	最低价格
            //c	String	收盘价格
            long ts = temp.getLong(0);
            String open = temp.getString(1);
            String high = temp.getString(2);
            String low = temp.getString(3);
            String close = temp.getString(4);
            double volCcy = temp.getDouble(6);
            barList.add(new Bar(DateUtil.formatDateTime(new Date(ts)), ts, instId, open, high, low, close, volCcy));
        }
    }

    public List<KLineEntity> parseKLineList(JSONObject barJson, String instId) {
        JSONArray dataArray = barJson.getJSONArray("data");
        List<KLineEntity> res = new ArrayList<>(300);
        for (int i = dataArray.size() - 1; i >= 0; i--) {
            JSONArray temp = dataArray.getJSONArray(i);
            //    ts	String	开始时间，Unix时间戳的毫秒数格式，如 1597026383085
            //o	String	开盘价格
            //h	String	最高价格
            //l	String	最低价格
            //c	String	收盘价格
            long ts = temp.getLong(0);
            BigDecimal open = temp.getBigDecimal(1);
            BigDecimal high = temp.getBigDecimal(2);
            BigDecimal low = temp.getBigDecimal(3);
            BigDecimal close = temp.getBigDecimal(4);
            float volCcy = temp.getFloat(6);
            KLineEntity kLineEntity = new KLineEntity();
            kLineEntity.setDate(DateUtil.formatDateTime(new Date(ts)));
            kLineEntity.setOpen(open);
            kLineEntity.setHigh(high);
            kLineEntity.setLow(low);
            kLineEntity.setClose(close);
            kLineEntity.setVolume(volCcy);
            kLineEntity.setInstId(instId);
            kLineEntity.setTs(ts);
            res.add(kLineEntity);
        }
        return res;
    }

    public List<KLineEntity> getKline(String instId, String type) {
        return getKline(instId, type, "300");
    }

    public List<KLineEntity> getKline(String instId, String type, String size) {
        return getKline(instId, type, size, true);
    }

    public List<KLineEntity> getKline(String instId, String type, String size, boolean reverse) {
        JSONObject barJson15 = marketDataAPIService.getCandlesticks(instId, null, null, type, size);
        List<KLineEntity> kLineEntities = parseKLineList(barJson15, instId);
        DataHelper.calculate(kLineEntities);
        if (reverse) {
            Collections.reverse(kLineEntities);
        }
        return kLineEntities;
    }

    public List<KLineEntity> getKline(String instId, String type, String size, String start, String end, boolean reverse) {
        JSONObject barJson15 = marketDataAPIService.getCandlesticks(instId, start, end, type, size);
        List<KLineEntity> kLineEntities = parseKLineList(barJson15, instId);
        DataHelper.calculate(kLineEntities);
        if (reverse) {
            Collections.reverse(kLineEntities);
        }
        return kLineEntities;
    }

    public double calculateTrend(double[] arr) {
        WeightedObservedPoints obs = new WeightedObservedPoints();
        for (int i = 0; i < arr.length; i++) {
            obs.add(i, arr[i]);
        }

        PolynomialCurveFitter fitter = PolynomialCurveFitter.create(1); // 使用一次多项式进行拟合
        double[] coeff = fitter.fit(obs.toList());

        return coeff[1]; // 返回一次项系数作为趋势指标
    }

    //        trade("TON-USDT-SWAP", "buy", "2" , "short");    平空
//        trade("TON-USDT-SWAP", "sell", "2" , "long");    平多
//        trade("TON-USDT-SWAP", "buy", "2" , "long");    开多
//        trade("TON-USDT-SWAP", "sell", "2" , "short");   开空
    public String trade(String instId, String side, String sz, String posSide, boolean check) {
        if (check && checkIsTrade(instId, posSide)) {
            return "";
        }
        log.info("trade  instId:{} side:{} ,sz:{},posSide:{}", instId, side, sz, posSide);
        PlaceOrder placeOrder = new PlaceOrder();
        placeOrder.setInstId(instId);
        placeOrder.setTdMode("cross");
        placeOrder.setSide(side);
        if (StrUtil.isNotEmpty(posSide)) {
            placeOrder.setPosSide(posSide);
        }
        placeOrder.setOrdType("market");
        placeOrder.setSz(sz);
        JSONObject jsonObject = tradeAPIService.placeOrder(placeOrder);
        log.info("sz:" + sz + "  委托结果:" + jsonObject.toJSONString());
        return jsonObject.getJSONArray("data").getJSONObject(0).getString("ordId");
    }

    public String limitTrade(String instId, String side, String sz, String posSide, String px) {
        if (checkIsTrade(instId, posSide)) {
            return "";
        }
        log.info("trade  instId:{} side:{} ,sz:{},posSide:{},px:{}", instId, side, sz, posSide, px);

        PlaceOrder placeOrder = new PlaceOrder();
        placeOrder.setInstId(instId);
        placeOrder.setTdMode("cross");
        placeOrder.setSide(side);
        if (StrUtil.isNotEmpty(posSide)) {
            placeOrder.setPosSide(posSide);
        }
        if (StrUtil.isNotEmpty(px)) {
            placeOrder.setPx(px);
        }
        placeOrder.setOrdType("limit");
        placeOrder.setSz(sz);
        JSONObject jsonObject = tradeAPIService.placeOrder(placeOrder);
        log.info("sz:" + sz + "  委托结果:" + jsonObject.toJSONString());
        return jsonObject.getJSONArray("data").getJSONObject(0).getString("ordId");
    }

    public String limitTrade(String instId, String side, String sz, String posSide, String px, boolean check) {
        if (check && checkIsTrade(instId, posSide)) {
            return "";
        }
        log.info("trade  instId:{} side:{} ,sz:{},posSide:{},px:{}", instId, side, sz, posSide, px);

        PlaceOrder placeOrder = new PlaceOrder();
        placeOrder.setInstId(instId);
        placeOrder.setTdMode("cross");
        placeOrder.setSide(side);
        if (StrUtil.isNotEmpty(posSide)) {
            placeOrder.setPosSide(posSide);
        }
        if (StrUtil.isNotEmpty(px)) {
            placeOrder.setPx(px);
        }
        placeOrder.setOrdType("limit");
        placeOrder.setSz(sz);
        JSONObject jsonObject = tradeAPIService.placeOrder(placeOrder);
        log.info("sz:" + sz + "  委托结果:" + jsonObject.toJSONString());
        return jsonObject.getJSONArray("data").getJSONObject(0).getString("ordId");
    }

    /**
     * 设置止盈止损
     *
     * @param instId
     * @param side
     * @param sz
     * @param posSide
     * @param winPx
     */
    public void algoTradeWin(String instId, String side, String sz, String posSide, String winPx) {
        PlaceAlgoOrder placeAlgoOrder = new PlaceAlgoOrder();
        placeAlgoOrder.setInstId(instId);
        placeAlgoOrder.setTdMode("cross");
//        placeAlgoOrder.setCcy("");
        placeAlgoOrder.setSide(side);
        placeAlgoOrder.setPosSide(posSide);
        placeAlgoOrder.setOrdType("conditional");
        placeAlgoOrder.setSz(sz);
        placeAlgoOrder.setReduceOnly(false);

//        止盈止损 Stop Order
        placeAlgoOrder.setTpTriggerPx(winPx);
        placeAlgoOrder.setTpOrdPx("-1");

//        计划委托 Trigger Order
//        placeAlgoOrder.setTriggerPx("1.1");
//        placeAlgoOrder.setOrderPx("0.7");


        JSONObject result = tradeAPIService.placeAlgoOrder(placeAlgoOrder);
        log.info("止盈止损委托结果:" + result.toJSONString());
    }

    public void algoTradeLoss(String instId, String side, String sz, String posSide, String lossPx) {
        PlaceAlgoOrder placeAlgoOrder = new PlaceAlgoOrder();
        placeAlgoOrder.setInstId(instId);
        placeAlgoOrder.setTdMode("cross");
//        placeAlgoOrder.setCcy("");
        placeAlgoOrder.setSide(side);
        placeAlgoOrder.setPosSide(posSide);
        placeAlgoOrder.setOrdType("conditional");
        placeAlgoOrder.setSz(sz);
        placeAlgoOrder.setReduceOnly(false);

//        止盈止损 Stop Order
        placeAlgoOrder.setSlTriggerPx(lossPx);
        placeAlgoOrder.setSlOrdPx("-1");

//        计划委托 Trigger Order
//        placeAlgoOrder.setTriggerPx("1.1");
//        placeAlgoOrder.setOrderPx("0.7");


        JSONObject result = tradeAPIService.placeAlgoOrder(placeAlgoOrder);
        log.info("止盈止损委托结果:" + result.toJSONString());
    }

    public boolean limitSell(String instId, String side, String sz, String posSide, String px) {
        log.info("trade  instId:{} side:{} ,sz:{},posSide:{},px:{}", instId, side, sz, posSide, px);

        PlaceOrder placeOrder = new PlaceOrder();
        placeOrder.setInstId(instId);
        placeOrder.setTdMode("cross");
        placeOrder.setSide(side);
        if (StrUtil.isNotEmpty(posSide)) {
            placeOrder.setPosSide(posSide);
        }
        if (StrUtil.isNotEmpty(px)) {
            placeOrder.setPx(px);
        }
        placeOrder.setOrdType("limit");
        placeOrder.setSz(sz);
        JSONObject jsonObject = tradeAPIService.placeOrder(placeOrder);
        log.info("sz:" + sz + "  委托结果:" + jsonObject.toJSONString());
        return true;
    }

    private boolean checkIsTrade(String instId, String posSide) {
        JSONObject posObj = accountAPIService.getPositions("SWAP", null, null);
        JSONArray data = posObj.getJSONArray("data");
        if (data.isEmpty()) {
            return false;
        }
        List<Position> position = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obg = data.getJSONObject(i);
            position.add(obg.toJavaObject(Position.class));
        }

        if (position.stream().map(Position::getInstId).collect(Collectors.toList()).contains(instId)
            && position.stream().anyMatch(s -> s.getInstId().equals(instId) && s.getPosSide().equals(posSide))) {
            return true;
        }
        JSONObject swap = tradeAPIService.getOrderList("SWAP", null, instId, null, null, null, null, null);
        return !swap.getJSONArray("data").isEmpty();
    }

    public double calculateEMA(double[] data, int period) {
        double smoothingFactor = 2.0 / (period + 1);
        double ema = data[0];
        for (int i = 1; i < data.length; i++) {
            ema = (data[i] - ema) * smoothingFactor + ema;
        }
        return ema;
    }

    public List<Double> calculateOBV(List<Double> closingPrices, List<Double> volumes) {
        List<Double> obvValues = new ArrayList<>();

        // 初始OBV值为0
        obvValues.add(0.0);

        for (int i = 1; i < closingPrices.size(); i++) {
            double priceChange = closingPrices.get(i) - closingPrices.get(i - 1);

            // 判断价格变动的方向
            int direction;
            if (priceChange > 0) {
                direction = 1;  // 价格上涨
            } else if (priceChange < 0) {
                direction = -1; // 价格下跌
            } else {
                direction = 0;  // 价格没有变动
            }

            // 根据价格变动的方向计算OBV值
            double obv = obvValues.get(i - 1);
            if (direction == 1) {
                obv += volumes.get(i);  // 价格上涨，OBV累加成交量
            } else if (direction == -1) {
                obv -= volumes.get(i);  // 价格下跌，OBV减去成交量
            }

            obvValues.add(obv);
        }

        return obvValues;
    }

    public String getSz(String instId, double close) {
        JSONObject instrument = getInstrument(instId);
        double ctVal = instrument.getDouble("ctVal");
        String lotSz = instrument.getString("lotSz");
        String minSz = instrument.getString("minSz");
        double per = RedisUtils.getCacheObject("per");
        if (btcEth.contains(instId)) {
            per = per * 10;
        }
        int scale = new BigDecimal(lotSz).scale();
        return BigDecimal.valueOf(Math.max(per / close / ctVal, Double.parseDouble(minSz)))
            .setScale(scale, RoundingMode.HALF_UP).toString();
    }

    public String getPosPercent(String instId, String sz, double percent) {
        JSONObject instrument = getInstrument(instId);
        String lotSz = instrument.getString("lotSz");
        int scale = new BigDecimal(lotSz).scale();
        return BigDecimal.valueOf(Double.parseDouble(sz) * percent)
            .setScale(scale, RoundingMode.HALF_UP).toString();
    }

    public double getAmplitude(List<KLineEntity> kLine) {
        return (kLine.get(0).getHigh().doubleValue() - kLine.get(0).getLow().doubleValue()) / kLine.get(0).getOpen()
            .doubleValue();
    }


    /**
     * 是否不对称单调
     *
     * @param list 数组
     * @param side 方向 true  递增  false  递减
     * @return
     */
    public boolean isMonotonically(List<Double> list, boolean side) {
        if (list == null || list.size() < 2) {
            return false;
        }
        boolean flag = false;
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i) > list.get(i - 1) && side) {
                if (flag) {
                    return false;
                }
                flag = true;
            }
            if (list.get(i) < list.get(i - 1) && !side) {
                if (flag) {
                    return false;
                }
                flag = true;
            }
        }
        return true;
    }

    /**
     * 是否对称单调
     *
     * @param list 数组
     * @param side 方向 true  递增  false  递减
     * @return
     */
    public static boolean isSymmetricMonotonically(List<Double> list, boolean side) {
        if (list == null || list.size() < 2) {
            return false;
        }
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i) > list.get(i - 1) && side) {
                return false;
            }
            if (list.get(i) < list.get(i - 1) && !side) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据当前持仓数量，返回可卖的最大仓位（已处理合约最小单位和精度）
     *
     * @param instId 合约ID
     * @param pos    持仓数量（double类型）
     * @return 可卖的最大仓位字符串，已按合约最小单位和精度处理
     */
    public String getMaxSellableSz(String instId, double pos) {
        JSONObject instrument = getInstrument(instId);
        String lotSz = instrument.getString("lotSz");
        String minSz = instrument.getString("minSz");
        int scale = new BigDecimal(lotSz).scale();
        // 取最大值和最小单位做比较，防止小于最小单位
        double maxSell = Math.max(pos, Double.parseDouble(minSz));
        return BigDecimal.valueOf(maxSell).setScale(scale, RoundingMode.HALF_UP).toString();
    }

}
