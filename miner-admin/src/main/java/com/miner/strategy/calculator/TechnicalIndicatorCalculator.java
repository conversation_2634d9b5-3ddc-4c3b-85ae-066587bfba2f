package com.miner.strategy.calculator;

import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 技术指标计算器
 * 负责各种技术指标的计算，支持缓存机制
 */
@Component
@Slf4j
public class TechnicalIndicatorCalculator {

    // 缓存计算结果，key格式: instId:timeframe:indicator:period
    private final ConcurrentHashMap<String, IndicatorResult> cache = new ConcurrentHashMap<>();

    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRE_TIME = 60000; // 1分钟

    /**
     * 计算EMA指标（带缓存）
     */
    public double calculateEMA(String instId, String timeframe, List<KLineEntity> klineList, int period) {
        String cacheKey = buildCacheKey(instId, timeframe, "EMA", period);

        IndicatorResult cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        List<Double> closePrices = extractClosePrices(klineList);
        double ema = calculateEMAInternal(closePrices, 0, period);

        cache.put(cacheKey, new IndicatorResult(ema, System.currentTimeMillis()));
        return ema;
    }

    /**
     * 计算RSI指标（带缓存）
     */
    public double calculateRSI(String instId, String timeframe, List<KLineEntity> klineList, int period) {
        String cacheKey = buildCacheKey(instId, timeframe, "RSI", period);

        IndicatorResult cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        List<Double> closePrices = extractClosePrices(klineList);
        double rsi = calculateRSIInternal(closePrices, 0, period);

        cache.put(cacheKey, new IndicatorResult(rsi, System.currentTimeMillis()));
        return rsi;
    }

    /**
     * 计算ATR指标（带缓存）
     */
    public double calculateATR(String instId, String timeframe, List<KLineEntity> klineList, int period) {
        String cacheKey = buildCacheKey(instId, timeframe, "ATR", period);

        IndicatorResult cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double atr = calculateATRInternal(klineList, 0, period);

        cache.put(cacheKey, new IndicatorResult(atr, System.currentTimeMillis()));
        return atr;
    }

    /**
     * 计算ADX指标（带缓存）
     */
    public double calculateADX(String instId, String timeframe, List<KLineEntity> klineList, int period) {
        String cacheKey = buildCacheKey(instId, timeframe, "ADX", period);

        IndicatorResult cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double adx = calculateADXInternal(klineList, 0, period);

        cache.put(cacheKey, new IndicatorResult(adx, System.currentTimeMillis()));
        return adx;
    }

    /**
     * 计算MACD指标（带缓存）
     */
    public double[] calculateMACD(String instId, String timeframe, List<KLineEntity> klineList) {
        String cacheKey = buildCacheKey(instId, timeframe, "MACD", 0);

        IndicatorResult cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }

        List<Double> closePrices = extractClosePrices(klineList);
        double[] macd = calculateMACDInternal(closePrices);

        cache.put(cacheKey, new IndicatorResult(0, System.currentTimeMillis(), macd));
        return macd;
    }

    /**
     * 提取收盘价列表
     */
    public List<Double> extractClosePrices(List<KLineEntity> klineList) {
        return klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());
    }

    /**
     * 清理过期缓存
     */
    public void cleanExpiredCache() {
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    /**
     * 清空所有缓存
     */
    public void clearCache() {
        cache.clear();
    }

    // ==================== 私有方法 ====================

    private String buildCacheKey(String instId, String timeframe, String indicator, int period) {
        return String.format("%s:%s:%s:%d", instId, timeframe, indicator, period);
    }

    private double calculateEMAInternal(List<Double> prices, int offset, int period) {
        if (prices.size() < offset + period) {
            return 0;
        }

        double alpha = 2.0 / (period + 1);
        double ema = prices.get(offset + period - 1);

        for (int i = offset + period - 2; i >= offset; i--) {
            ema = alpha * prices.get(i) + (1 - alpha) * ema;
        }

        return ema;
    }

    private double calculateRSIInternal(List<Double> prices, int offset, int period) {
        if (prices.size() < offset + period + 1) {
            return 50.0;
        }

        double gainSum = 0.0;
        double lossSum = 0.0;

        for (int i = offset + 1; i <= offset + period; i++) {
            double change = prices.get(i) - prices.get(i - 1);
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += Math.abs(change);
            }
        }

        if (lossSum == 0) return 100.0;
        if (gainSum == 0) return 0.0;

        double avgGain = gainSum / period;
        double avgLoss = lossSum / period;
        double rs = avgGain / avgLoss;

        return 100.0 - (100.0 / (1.0 + rs));
    }

    private double calculateATRInternal(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0.0;
        }

        double atrSum = 0.0;
        for (int i = offset; i < offset + period; i++) {
            KLineEntity current = klineList.get(i);
            KLineEntity previous = klineList.get(i + 1);

            double high = current.getHigh().doubleValue();
            double low = current.getLow().doubleValue();
            double prevClose = previous.getClose().doubleValue();

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(Math.max(tr1, tr2), tr3);

            atrSum += tr;
        }

        return atrSum / period;
    }

    private double calculateADXInternal(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 5) {
            return 0.0;
        }

        // 简化的ADX计算实现
        double sumTR = 0.0;
        double sumPlusDM = 0.0;
        double sumMinusDM = 0.0;

        for (int i = offset; i < offset + period; i++) {
            KLineEntity current = klineList.get(i);
            KLineEntity previous = klineList.get(i + 1);

            double high = current.getHigh().doubleValue();
            double low = current.getLow().doubleValue();
            double prevClose = previous.getClose().doubleValue();
            double prevHigh = previous.getHigh().doubleValue();
            double prevLow = previous.getLow().doubleValue();

            // 计算TR
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(Math.max(tr1, tr2), tr3);
            sumTR += tr;

            // 计算DM
            double upMove = high - prevHigh;
            double downMove = prevLow - low;

            if (upMove > downMove && upMove > 0) {
                sumPlusDM += upMove;
            }
            if (downMove > upMove && downMove > 0) {
                sumMinusDM += downMove;
            }
        }

        if (sumTR == 0) return 0.0;

        double plusDI = (sumPlusDM / sumTR) * 100;
        double minusDI = (sumMinusDM / sumTR) * 100;
        double dx = Math.abs(plusDI - minusDI) / (plusDI + minusDI) * 100;

        return dx;
    }

    private double[] calculateMACDInternal(List<Double> prices) {
        if (prices.size() < 26) {
            return new double[]{0, 0, 0};
        }

        double ema12 = calculateEMAInternal(prices, 0, 12);
        double ema26 = calculateEMAInternal(prices, 0, 26);
        double macdLine = ema12 - ema26;

        // 简化的信号线计算
        double signalLine = macdLine * 0.9; // 简化实现
        double histogram = macdLine - signalLine;

        return new double[]{macdLine, signalLine, histogram};
    }

    /**
     * 指标计算结果缓存类
     */
    private static class IndicatorResult {
        private final double value;
        private final long timestamp;
        private final Object rawValue;

        public IndicatorResult(double value, long timestamp) {
            this.value = value;
            this.timestamp = timestamp;
            this.rawValue = null;
        }

        public IndicatorResult(double value, long timestamp, Object rawValue) {
            this.value = value;
            this.timestamp = timestamp;
            this.rawValue = rawValue;
        }

        public double getValue() {
            return value;
        }

        public Object getRawValue() {
            return rawValue;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
        }
    }
}
