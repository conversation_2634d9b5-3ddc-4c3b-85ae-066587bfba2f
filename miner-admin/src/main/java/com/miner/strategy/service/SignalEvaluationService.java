package com.miner.strategy.service;

import com.miner.strategy.calculator.TechnicalIndicatorCalculator;
import com.miner.strategy.signal.SignalCalculatorFactory;
import com.miner.strategy.signal.SignalStrengthCalculator;
import com.miner.strategy.signal.SignalStrengthCalculator.SignalDirection;
import com.miner.system.indicator.KLineEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信号评估服务
 * 负责综合评估各种技术指标信号强度
 */
@Service
@Slf4j
public class SignalEvaluationService {

    @Autowired
    private SignalCalculatorFactory calculatorFactory;

    @Autowired
    private TechnicalIndicatorCalculator indicatorCalculator;

    /**
     * 评估做多信号强度
     */
    public SignalEvaluationResult evaluateLongSignal(List<KLineEntity> klineList) {
        return evaluateSignal(klineList, SignalDirection.LONG);
    }

    /**
     * 评估做空信号强度
     */
    public SignalEvaluationResult evaluateShortSignal(List<KLineEntity> klineList) {
        return evaluateSignal(klineList, SignalDirection.SHORT);
    }

    /**
     * 评估信号强度
     */
    private SignalEvaluationResult evaluateSignal(List<KLineEntity> klineList, SignalDirection direction) {
        if (klineList == null || klineList.isEmpty()) {
            return new SignalEvaluationResult(0.0, new HashMap<>(), false);
        }

        String instId = klineList.get(0).getInstId();
        Map<String, SignalStrengthCalculator> calculators = getCalculatorsForDirection(direction);

        Map<String, Double> signalStrengths = new HashMap<>();
        double totalScore = 0.0;
        double maxPossibleScore = 0.0;

        // 计算各个指标的信号强度
        for (Map.Entry<String, SignalStrengthCalculator> entry : calculators.entrySet()) {
            String name = entry.getKey();
            SignalStrengthCalculator calculator = entry.getValue();

            try {
                double strength = calculator.calculateStrength(klineList, direction);
                double weight = calculator.getWeight();

                signalStrengths.put(name, strength);
                totalScore += strength * weight;
                maxPossibleScore += weight;

                log.debug("{} {} 信号强度: {} (权重: {})", instId, name,
                    String.format("%.2f", strength), String.format("%.1f", weight));

            } catch (Exception e) {
                log.error("计算 {} {} 信号强度时发生异常", instId, name, e);
                signalStrengths.put(name, 0.0);
                maxPossibleScore += calculator.getWeight();
            }
        }

        // 计算综合评分比例（满分10分）
        double scoreRatio = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 10.0 : 0.0;

        // 检查是否有强信号组合
        boolean hasStrongSignal = checkStrongSignalCombination(signalStrengths, direction);

        log.info("{} {} 信号评估: 总分={}/{} = {}/10, 强信号={}",
            instId, direction.name(),
            String.format("%.1f", totalScore),
            String.format("%.1f", maxPossibleScore),
            String.format("%.1f", scoreRatio),
            hasStrongSignal ? "是" : "否");

        return new SignalEvaluationResult(scoreRatio, signalStrengths, hasStrongSignal);
    }

    /**
     * 检查强信号组合
     */
    private boolean checkStrongSignalCombination(Map<String, Double> signalStrengths, SignalDirection direction) {
        if (direction == SignalDirection.LONG) {
            return checkLongStrongSignals(signalStrengths);
        } else {
            return checkShortStrongSignals(signalStrengths);
        }
    }

    /**
     * 检查做多强信号组合
     */
    private boolean checkLongStrongSignals(Map<String, Double> signals) {
        double threshold = 0.55;

        // 强信号组合1: ADX强 + MACD金叉 + RSI超卖回升
        if (getSignalValue(signals, "ADX") > 0.6 &&
            getSignalValue(signals, "MACD") > threshold &&
            getSignalValue(signals, "RSI") > threshold) {
            return true;
        }

        // 强信号组合2: 看涨K线形态强 + 价格突破 + 成交量配合
        if (getSignalValue(signals, "CANDLESTICK") > 0.6 &&
            getSignalValue(signals, "PRICE_BREAKOUT") > threshold &&
            getSignalValue(signals, "VOLUME") > threshold) {
            return true;
        }

        // 强信号组合3: 布林带触底反弹 + RSI超卖回升 + ADX中等以上
        if (getSignalValue(signals, "BOLLINGER") > 0.6 &&
            getSignalValue(signals, "RSI") > threshold &&
            getSignalValue(signals, "ADX") > 0.4) {
            return true;
        }

        return false;
    }

    /**
     * 检查做空强信号组合
     */
    private boolean checkShortStrongSignals(Map<String, Double> signals) {
        double threshold = 0.5;

        // 强信号组合1: ADX强 + MACD死叉 + RSI超买回落
        if (getSignalValue(signals, "ADX_SHORT") > 0.6 &&
            getSignalValue(signals, "MACD_SHORT") > threshold &&
            getSignalValue(signals, "RSI_SHORT") > threshold) {
            return true;
        }

        // 强信号组合2: 看跌K线形态强 + 价格跌破 + 成交量配合
        if (getSignalValue(signals, "CANDLESTICK_SHORT") > 0.6 &&
            getSignalValue(signals, "PRICE_BREAKDOWN") > threshold &&
            getSignalValue(signals, "VOLUME_SHORT") > threshold) {
            return true;
        }

        // 强信号组合3: 布林带触顶回落 + RSI超买回落 + ADX中等以上
        if (getSignalValue(signals, "BOLLINGER_SHORT") > 0.6 &&
            getSignalValue(signals, "RSI_SHORT") > threshold &&
            getSignalValue(signals, "ADX_SHORT") > 0.4) {
            return true;
        }

        return false;
    }

    private double getSignalValue(Map<String, Double> signals, String key) {
        return signals.getOrDefault(key, 0.0);
    }

    private Map<String, SignalStrengthCalculator> getCalculatorsForDirection(SignalDirection direction) {
        if (direction == SignalDirection.LONG) {
            return calculatorFactory.getLongCalculators();
        } else {
            return calculatorFactory.getShortCalculators();
        }
    }

    /**
     * 信号评估结果
     */
    @Data
    public static class SignalEvaluationResult {
        private final double scoreRatio;
        private final Map<String, Double> signalStrengths;
        private final boolean hasStrongSignal;

        public SignalEvaluationResult(double scoreRatio, Map<String, Double> signalStrengths, boolean hasStrongSignal) {
            this.scoreRatio = scoreRatio;
            this.signalStrengths = signalStrengths;
            this.hasStrongSignal = hasStrongSignal;
        }

        public double getScoreRatio() {
            return scoreRatio;
        }

        public Map<String, Double> getSignalStrengths() {
            return signalStrengths;
        }

        public boolean hasStrongSignal() {
            return hasStrongSignal;
        }

        public double getSignalStrength(String signalName) {
            return signalStrengths.getOrDefault(signalName, 0.0);
        }
    }
}
