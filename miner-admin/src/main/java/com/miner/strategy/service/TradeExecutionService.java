package com.miner.strategy.service;

import com.miner.strategy.BaseStrategy;
import com.miner.strategy.calculator.TechnicalIndicatorCalculator;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.okx.bean.Position;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.List;

/**
 * 交易执行服务
 * 负责处理具体的交易执行逻辑
 */
@Service
@Slf4j
public class TradeExecutionService extends BaseStrategy {

    @Autowired
    private TechnicalIndicatorCalculator indicatorCalculator;

    // ATR参数
    private static final int ATR_PERIOD = 14;
    private static final double STOP_LOSS_ATR_MULTIPLIER = 2.0;
    private static final double SHORT_STOP_LOSS_ATR_MULTIPLIER = 1.5;
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_1 = 10.0;
    private static final double TAKE_PROFIT_ATR_MULTIPLIER_2 = 25.0;



    @Override
    public void run() {

    }


    /**
     * 执行做多入场
     */
    public boolean executeLongEntry(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        try {
            log.info("准备做多入场: instId={}, 当前价格={}", instId, currentPrice);

            // 计算止盈止损价格
            StopLevelDTO stopLevels = calculateLongStopLevels(instId, currentPrice, klineList);
            if (stopLevels == null) {
                return false;
            }

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());
            log.info("做多入场参数: 交易对={}, 价格={}, 数量={}, 止损价={}, 止盈价1={}, 止盈价2={}",
                instId, currentPrice, sz, stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2);

            // 执行开仓
            trade(instId, "buy", sz, "long", false);
            Thread.sleep(100);

            // 设置止盈止损
            setupLongStopOrders(instId, currentPrice, stopLevels);

            return true;
        } catch (Exception e) {
            log.error("执行做多入场操作异常", e);
            return false;
        }
    }

    /**
     * 执行做空入场
     */
    public boolean executeShortEntry(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        try {
            log.info("准备做空入场: instId={}, 当前价格={}", instId, currentPrice);

            // 计算止盈止损价格
            StopLevelDTO stopLevels = calculateShortStopLevels(instId, currentPrice, klineList);
            if (stopLevels == null) {
                return false;
            }

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());
            log.info("做空入场参数: 交易对={}, 价格={}, 数量={}, 止损价={}, 止盈价1={}, 止盈价2={}",
                instId, currentPrice, sz, stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2);

            // 执行开仓
            trade(instId, "sell", sz, "short", false);
            Thread.sleep(100);

            // 设置止盈止损
            setupShortStopOrders(instId, currentPrice, stopLevels);

            return true;
        } catch (Exception e) {
            log.error("执行做空入场操作异常", e);
            return false;
        }
    }

    /**
     * 计算做多止盈止损价格
     */
    private StopLevelDTO calculateLongStopLevels(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        // 计算ATR用于设置止损和止盈
        double atr = indicatorCalculator.calculateATR(instId, "5m", klineList, ATR_PERIOD);
        if (atr <= 0) {
            log.info("ATR计算错误，取消做多入场");
            return null;
        }

        // 设置止损价
        BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() - atr * STOP_LOSS_ATR_MULTIPLIER)
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 计算止盈价格
        BigDecimal takeProfitPrice1 = currentPrice.add(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_1))
            .setScale(currentPrice.scale(), RoundingMode.UP);
        BigDecimal takeProfitPrice2 = currentPrice.add(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_2))
            .setScale(currentPrice.scale(), RoundingMode.UP);

        return new StopLevelDTO(stopLossPrice, takeProfitPrice1, takeProfitPrice2);
    }

    /**
     * 计算做空止盈止损价格
     */
    private StopLevelDTO calculateShortStopLevels(String instId, BigDecimal currentPrice, List<KLineEntity> klineList) {
        // 计算ATR用于设置止损和止盈
        double atr = indicatorCalculator.calculateATR(instId, "5m", klineList, ATR_PERIOD);
        if (atr <= 0) {
            log.info("ATR计算错误，取消做空入场");
            return null;
        }

        // 设置止损价（做空止损在价格上方）
        BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() + atr * SHORT_STOP_LOSS_ATR_MULTIPLIER)
            .setScale(currentPrice.scale(), RoundingMode.UP);

        // 计算止盈价格（做空止盈在价格下方）
        BigDecimal takeProfitPrice1 = currentPrice.subtract(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_1))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal takeProfitPrice2 = currentPrice.subtract(new BigDecimal(atr * TAKE_PROFIT_ATR_MULTIPLIER_2))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        return new StopLevelDTO(stopLossPrice, takeProfitPrice1, takeProfitPrice2);
    }

    /**
     * 设置做多止盈止损订单
     */
    private void setupLongStopOrders(String instId, BigDecimal currentPrice, StopLevelDTO stopLevels) {
        // 获取持仓信息
        Position pos = getPos(instId);
        if (pos == null) {
            log.info("未获取到做多持仓信息，无法设置止盈止损");
            return;
        }

        log.info("做多开仓成功: 交易对={}, 持仓ID={}, 数量={}", instId, pos.getPosId(), pos.getAvailPos());

        // 计算各止盈档位的仓位大小
        BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
        BigDecimal tp1Size = totalPos.multiply(BigDecimal.valueOf(0.5))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal tp2Size = totalPos.subtract(tp1Size)
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 设置止损单
        String stopLossPx = stopLevels.stopLossPrice.toString();
        algoTradeLoss(instId, "sell", totalPos.toString(), "long", stopLossPx);
        log.info("做多止损单设置成功: 交易对={}, 价格={}, 数量={}", instId, stopLossPx, totalPos);

        // 设置止盈单
        String tp1Px = stopLevels.takeProfitPrice1.toString();
        String tp2Px = stopLevels.takeProfitPrice2.toString();

        algoTradeWin(instId, "sell", getMaxSellableSz(instId, tp1Size.doubleValue()), "long", tp1Px);
        log.info("做多止盈单1设置成功: 交易对={}, 价格={}, 数量={}", instId, tp1Px, tp1Size);

        algoTradeWin(instId, "sell", getMaxSellableSz(instId, tp2Size.doubleValue()), "long", tp2Px);
        log.info("做多止盈单2设置成功: 交易对={}, 价格={}, 数量={}", instId, tp2Px, tp2Size);

        // 发送消息通知
        messageService.send(instId + "多单策略", MessageFormat.format("{0}多头入场: 入场价={1}, 止损价={2}, 止盈1={3}, 止盈2={4}",
            instId, new BigDecimal(pos.getAvgPx()).setScale(currentPrice.scale(), RoundingMode.DOWN),
            stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2));

        // 记录交易信息到Redis
        String key = "EMARetraceStrategy:" + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(12));
    }

    /**
     * 设置做空止盈止损订单
     */
    private void setupShortStopOrders(String instId, BigDecimal currentPrice, StopLevelDTO stopLevels) {
        // 获取持仓信息
        Position pos = getPos(instId);
        if (pos == null) {
            log.info("未获取到做空持仓信息，无法设置止盈止损");
            return;
        }

        log.info("做空开仓成功: 交易对={}, 持仓ID={}, 数量={}", instId, pos.getPosId(), pos.getAvailPos());

        // 计算各止盈档位的仓位大小
        BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
        BigDecimal tp1Size = totalPos.multiply(BigDecimal.valueOf(0.5))
            .setScale(currentPrice.scale(), RoundingMode.DOWN);
        BigDecimal tp2Size = totalPos.subtract(tp1Size)
            .setScale(currentPrice.scale(), RoundingMode.DOWN);

        // 设置止损单
        String stopLossPx = stopLevels.stopLossPrice.toString();
        algoTradeLoss(instId, "buy", totalPos.toString(), "short", stopLossPx);
        log.info("做空止损单设置成功: 交易对={}, 价格={}, 数量={}", instId, stopLossPx, totalPos);

        // 设置止盈单
        String tp1Px = stopLevels.takeProfitPrice1.toString();
        String tp2Px = stopLevels.takeProfitPrice2.toString();

        algoTradeWin(instId, "buy", getMaxSellableSz(instId, tp1Size.doubleValue()), "short", tp1Px);
        log.info("做空止盈单1设置成功: 交易对={}, 价格={}, 数量={}", instId, tp1Px, tp1Size);

        algoTradeWin(instId, "buy", getMaxSellableSz(instId, tp2Size.doubleValue()), "short", tp2Px);
        log.info("做空止盈单2设置成功: 交易对={}, 价格={}, 数量={}", instId, tp2Px, tp2Size);

        // 发送消息通知
        messageService.send(instId + "空单策略", MessageFormat.format("{0}空头入场: 入场价={1}, 止损价={2}, 止盈1={3}, 止盈2={4}",
            instId, new BigDecimal(pos.getAvgPx()).setScale(currentPrice.scale(), RoundingMode.DOWN),
            stopLevels.stopLossPrice, stopLevels.takeProfitPrice1, stopLevels.takeProfitPrice2));

        // 记录交易信息到Redis
        String key = "EMARetraceShortStrategy:" + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, pos.getPosId(), Duration.ofHours(12));
    }

    /**
     * 止盈止损价格DTO
     */
    public static class StopLevelDTO {
        public final BigDecimal stopLossPrice;
        public final BigDecimal takeProfitPrice1;
        public final BigDecimal takeProfitPrice2;

        public StopLevelDTO(BigDecimal stopLossPrice, BigDecimal takeProfitPrice1, BigDecimal takeProfitPrice2) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice1 = takeProfitPrice1;
            this.takeProfitPrice2 = takeProfitPrice2;
        }
    }
}
