package com.miner.strategy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * EMA策略配置类
 * 支持配置热更新，避免硬编码参数
 */
@Component
@ConfigurationProperties(prefix = "strategy.ema")
@Data
public class EMAStrategyConfig {

    // ==================== EMA参数 ====================
    
    /**
     * 快速EMA周期
     */
    private int emaFast = 9;
    
    /**
     * 中期EMA周期
     */
    private int emaMid = 21;
    
    /**
     * 慢速EMA周期
     */
    private int emaSlow = 55;

    // ==================== 时间框架 ====================
    
    /**
     * 主周期时间框架
     */
    private String mainTimeframe = "4H";
    
    /**
     * 次周期时间框架
     */
    private String subTimeframe = "5m";

    // ==================== 回调参数 ====================
    
    /**
     * 做多回调深度比例
     */
    private double retraceDepth = 0.8;
    
    /**
     * 做空回调深度比例
     */
    private double shortRetraceDepth = 0.8;

    // ==================== ATR参数 ====================
    
    /**
     * ATR计算周期
     */
    private int atrPeriod = 14;
    
    /**
     * 做多止损ATR倍数
     */
    private double stopLossAtrMultiplier = 2.0;
    
    /**
     * 做空止损ATR倍数
     */
    private double shortStopLossAtrMultiplier = 1.5;
    
    /**
     * 第一档止盈ATR倍数
     */
    private double takeProfitAtrMultiplier1 = 10.0;
    
    /**
     * 第二档止盈ATR倍数
     */
    private double takeProfitAtrMultiplier2 = 25.0;

    // ==================== RSI参数 ====================
    
    /**
     * RSI计算周期
     */
    private int rsiPeriod = 14;
    
    /**
     * RSI超卖阈值
     */
    private double rsiOversold = 30.0;
    
    /**
     * RSI上升阈值
     */
    private double rsiRising = 40.0;
    
    /**
     * RSI超买阈值
     */
    private double rsiOverbought = 70.0;
    
    /**
     * RSI下降阈值
     */
    private double rsiFalling = 60.0;

    // ==================== ADX参数 ====================
    
    /**
     * ADX计算周期
     */
    private int adxPeriod = 14;
    
    /**
     * ADX强趋势阈值
     */
    private double adxStrongTrendThreshold = 25.0;

    // ==================== 斜率参数 ====================
    
    /**
     * EMA55下降斜率阈值（做空）
     */
    private double emaSlopeThreshold = -0.15;
    
    /**
     * BTC上升趋势斜率阈值
     */
    private double btcUptrendThreshold = 0.15;
    
    /**
     * BTC下降趋势斜率阈值
     */
    private double btcDowntrendThreshold = -0.15;

    // ==================== 信号强度阈值 ====================
    
    /**
     * 做多基础阈值
     */
    private double longBaseThreshold = 3.8;
    
    /**
     * 做空基础阈值
     */
    private double shortBaseThreshold = 3.9;
    
    /**
     * 做空强信号阈值
     */
    private double shortSignalThreshold = 0.5;
    
    /**
     * 强MACD阈值
     */
    private double strongMacdThreshold = 0.55;
    
    /**
     * 强布林带阈值
     */
    private double strongBollingerThreshold = 0.55;
    
    /**
     * 强RSI阈值
     */
    private double strongRsiThreshold = 0.55;
    
    /**
     * 强价格突破阈值
     */
    private double strongPriceBreakoutThreshold = 0.6;
    
    /**
     * 强形态阈值
     */
    private double strongPatternThreshold = 0.65;
    
    /**
     * 强OBV阈值
     */
    private double strongObvThreshold = 0.8;

    // ==================== 组合信号阈值 ====================
    
    /**
     * 组合信号阈值
     */
    private double combinedSignalThreshold = 0.45;
    
    /**
     * 组合成交量阈值
     */
    private double combinedVolumeThreshold = 0.5;

    // ==================== 量价分析参数 ====================
    
    /**
     * 量价分析最小K线数量
     */
    private int volumeAnalysisMinBars = 20;
    
    /**
     * OBV趋势分析K线数量
     */
    private int obvTrendBars = 10;
    
    /**
     * 成交量突破阈值
     */
    private double volumeBreakoutThreshold = 2.0;
    
    /**
     * 成交量增加阈值
     */
    private double volumeIncreaseThreshold = 1.3;
    
    /**
     * 成交量萎缩阈值
     */
    private double volumeShrinkThreshold = 0.7;

    // ==================== 缓存参数 ====================
    
    /**
     * 候选交易对过期时间（小时）
     */
    private int candidateExpireHours = 24;
    
    /**
     * 技术指标缓存过期时间（毫秒）
     */
    private long indicatorCacheExpireTime = 60000;

    // ==================== 风险控制参数 ====================
    
    /**
     * 最大交易对数量
     */
    private int maxTradingPairs = 40;
    
    /**
     * ADX最低要求
     */
    private double minAdxRequirement = 0.25;
    
    /**
     * 强信号阈值折扣
     */
    private double strongSignalDiscount = 0.85;
    
    /**
     * 接近阈值比例
     */
    private double nearThresholdRatio = 0.9;

    // ==================== 动态支撑位参数 ====================
    
    /**
     * 支撑位最小距离基础值
     */
    private double supportMinDistanceBase = 0.5;
    
    /**
     * 支撑位最大距离基础值
     */
    private double supportMaxDistanceBase = 1.5;
    
    /**
     * 支撑位最大距离上限
     */
    private double supportMaxDistanceLimit = 5.0;
    
    /**
     * 支撑位波动率系数1
     */
    private double supportVolatilityFactor1 = 0.5;
    
    /**
     * 支撑位波动率系数2
     */
    private double supportVolatilityFactor2 = 0.8;

    // ==================== 验证方法 ====================
    
    /**
     * 验证配置参数的有效性
     */
    public boolean validateConfig() {
        // 验证EMA周期
        if (emaFast >= emaMid || emaMid >= emaSlow) {
            return false;
        }
        
        // 验证阈值范围
        if (retraceDepth < 0.1 || retraceDepth > 1.0) {
            return false;
        }
        
        // 验证ATR倍数
        if (stopLossAtrMultiplier <= 0 || takeProfitAtrMultiplier1 <= 0) {
            return false;
        }
        
        // 验证RSI阈值
        if (rsiOversold >= rsiOverbought) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format("EMA(%d,%d,%d), ATR=%d, RSI=%d, 做多阈值=%.1f, 做空阈值=%.1f",
            emaFast, emaMid, emaSlow, atrPeriod, rsiPeriod, longBaseThreshold, shortBaseThreshold);
    }
}
