package com.miner.system.okx.service.message.impl;

import com.miner.system.okx.service.message.MessageService;
import com.miner.system.okx.service.message.PushPlusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MessageServiceImpl implements MessageService {


    @Resource
    PushPlusService pushPlusService;

    @Override
    public void send(String body) {
        pushPlusService.sendMsg("信号通知", body);
    }


    @Override
    public void send(String title, String body) {
        pushPlusService.sendMsg(title, body);
    }
}
