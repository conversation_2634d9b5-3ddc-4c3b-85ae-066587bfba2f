package com.miner.system.okx.service.marketData.impl;

import com.alibaba.fastjson.JSONObject;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

interface MarketDataAPI {

    //获取所有产品行情信息 Get Tickers
//参数
//     instType	String	是	产品类型        SPOT：币币
                                    //     SWAP：永续合约
                                    //     FUTURES：交割合约
                                    //     OPTION：期权

//    uly	String	否	标的指数
//    适用于交割/永续/期权，如 BTC-USD
//    instFamily	String	否	交易品种
//    适用于交割/永续/期权，如 BTC-USD


//    响应
//    instType	String	产品类型
//    instId	String	产品ID
//    last	String	最新成交价
//    lastSz	String	最新成交的数量
//    askPx	String	卖一价
//    askSz	String	卖一价的挂单数数量
//    bidPx	String	买一价
//    bidSz	String	买一价的挂单数量
//    open24h	String	24小时开盘价
//    high24h	String	24小时最高价
//    low24h	String	24小时最低价
//    volCcy24h	String	24小时成交量，以币为单位
//    如果是衍生品合约，数值为交易货币的数量。
//    如果是币币/币币杠杆，数值为计价货币的数量。
//    vol24h	String	24小时成交量，以张为单位
//    如果是衍生品合约，数值为合约的张数。
//    如果是币币/币币杠杆，数值为交易货币的数量。
//    sodUtc0	String	UTC 0 时开盘价
//    sodUtc8	String	UTC+8 时开盘价
//    ts	String	ticker数据产生时间，Unix时间戳的毫秒数格式，如 1597026383085
    @GET("/api/v5/market/tickers")
    Call<JSONObject> getTickers(@Query("instType") String instType, @Query("uly") String uly);

    //获取单个产品行情信息 Get Ticker
    @GET("/api/v5/market/ticker")
    Call<JSONObject> getTicker(@Query("instId") String instId);

    //获取指数行情数据 Get Index Tickers
    @GET("/api/v5/market/index-tickers")
    Call<JSONObject> getIndexTickers(@Query("quoteCcy") String quoteCcy, @Query("instId") String instId);


    //获取产品深度 Get Order Book
    @GET("/api/v5/market/books")
    Call<JSONObject> getOrderBook(@Query("instId") String instId, @Query("sz") String sz);

    //获取所有交易产品K线数据 Get Candlesticks
    @GET("/api/v5/market/candles")
    Call<JSONObject> getCandlesticks(@Query("instId") String instId, @Query("after") String after, @Query("before") String before, @Query("bar") String bar, @Query("limit") String limit);

    //获取交易产品历史K线数据（仅主流币） Get Candlesticks History（top currencies only）
    @GET("/api/v5/market/history-candles")
    Call<JSONObject> getCandlesticksHistory(@Query("instId") String instId, @Query("after") String after, @Query("before") String before, @Query("bar") String bar, @Query("limit") String limit);

    //获取指数K线数据 Get Index Candlesticks
    @GET("/api/v5/market/index-candles")
    Call<JSONObject> getIndexCandlesticks(@Query("instId") String instId, @Query("after") String after, @Query("before") String before, @Query("bar") String bar, @Query("limit") String limit);
    //获取历史指数K线数据 Get Index Candlesticks
    @GET("/api/v5/market/history-index-candles")
    Call<JSONObject> getHistoryIndexCandlesticks(@Query("instId") String instId, @Query("after") String after, @Query("before") String before, @Query("bar") String bar, @Query("limit") String limit);

    //获取标记价格K线数据 Get Mark Price Candlesticks
    @GET("/api/v5/market/mark-price-candles")
    Call<JSONObject> getMarkPriceCandlesticks(@Query("instId") String instId, @Query("after") String after, @Query("before") String before, @Query("bar") String bar, @Query("limit") String limit);


    //获取交易产品公共成交数据 Get Trades
    @GET("/api/v5/market/trades")
    Call<JSONObject> getTrades(@Query("instId") String instId, @Query("limit") String limit);

    //获取平台24小时总成交量 Get total volume
    @GET("/api/v5/market/platform-24-volume")
    Call<JSONObject> getTotalVolume();

}
