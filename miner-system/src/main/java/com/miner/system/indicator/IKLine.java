package com.miner.system.indicator;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public interface IKLine {

    String getDate();

    BigDecimal getOpenPrice();

    BigDecimal getHighPrice();

    BigDecimal getLowPrice();

    BigDecimal getClosePrice();

    double getRate();

    float getMA5Price();

    float getMA10Price();

    float getMA20Price();

    float getMA30Price();

    float getMA60Price();

    float getMA55Price();

    float getMA75Price();

    String getBuyText();

    float getDea();

    float getDif();

    float getMacd();
    
    /**
     * 获取MACD信号线
     */
    float getMacdSignal();

    /**
     * 获取MACD柱状图值
     */
    float getMacdHist();

    float getK();

    float getD();
    
    /**
     * 获取随机指标K值
     */
    float getStochK();
    
    /**
     * 获取随机指标D值
     */
    float getStochD();

    float getJ();

    float getR();

    float getRsi();

    float getUp();

    float getMb();

    float getDn();

    float getPercent();

    float getAmplitude();

    float getPushPrice();

    boolean isBuy();

    boolean isSell();

    boolean isPush();

    float getVolume();

    float getMA5Volume();

    float getMA10Volume();

    float getPdi();

    float getMdi();
}
